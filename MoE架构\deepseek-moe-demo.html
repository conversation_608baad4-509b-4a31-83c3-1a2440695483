<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek MoE架构交互演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .header p {
            font-size: 1.3em;
            color: #666;
            line-height: 1.6;
        }

        /* 控制面板 */
        .control-panel {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px solid #dee2e6;
        }

        .control-panel h3 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
            text-align: center;
        }

        .input-section {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
        }

        .input-box {
            padding: 15px 20px;
            border: 2px solid #1e3c72;
            border-radius: 15px;
            font-size: 1.1em;
            outline: none;
            transition: all 0.3s ease;
        }

        .input-box:focus {
            border-color: #2a5298;
            box-shadow: 0 0 20px rgba(30, 60, 114, 0.3);
        }

        .process-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .process-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(30, 60, 114, 0.4);
        }

        .process-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* MoE架构可视化 */
        .moe-architecture {
            position: relative;
            background: radial-gradient(circle at center, #f0f8ff, #e6f3ff);
            border-radius: 25px;
            padding: 40px;
            min-height: 600px;
            border: 3px solid #1e3c72;
            margin-bottom: 40px;
            overflow: hidden;
        }

        .architecture-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
            color: #1e3c72;
            font-weight: 800;
        }

        /* 输入层 */
        .input-layer {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px 30px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 1.2em;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
            z-index: 10;
            min-width: 200px;
            text-align: center;
        }

        /* 路由器 */
        .router {
            position: absolute;
            top: 150px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffc107, #ff8f00);
            color: white;
            padding: 25px;
            border-radius: 50%;
            font-weight: 800;
            font-size: 1.1em;
            box-shadow: 0 15px 40px rgba(255, 193, 7, 0.4);
            z-index: 10;
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        /* 专家模块 */
        .experts-container {
            position: absolute;
            top: 300px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 30px;
            z-index: 8;
        }

        .expert {
            background: white;
            border: 3px solid #6c757d;
            border-radius: 20px;
            padding: 20px;
            width: 150px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .expert.active {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5, #ffe6e6);
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(220, 53, 69, 0.3);
        }

        .expert.selected {
            border-color: #007bff;
            background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        .expert-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .expert-name {
            font-weight: 700;
            color: #333;
            font-size: 0.9em;
        }

        .expert-score {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: 700;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .expert.scored .expert-score {
            opacity: 1;
        }

        /* 输出层 */
        .output-layer {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #6f42c1, #5a2d91);
            color: white;
            padding: 20px 30px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 1.2em;
            box-shadow: 0 10px 30px rgba(111, 66, 193, 0.3);
            z-index: 10;
            min-width: 200px;
            text-align: center;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .output-layer.show {
            opacity: 1;
        }

        /* 连接线动画 */
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, #1e3c72, transparent);
            height: 4px;
            border-radius: 2px;
            z-index: 5;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .connection-line.active {
            opacity: 1;
            animation: flow 2s infinite;
        }

        @keyframes flow {
            0% {
                background: linear-gradient(90deg, transparent, #1e3c72, transparent);
            }

            50% {
                background: linear-gradient(90deg, transparent, #dc3545, transparent);
            }

            100% {
                background: linear-gradient(90deg, transparent, #1e3c72, transparent);
            }
        }

        /* 说明面板 */
        .explanation-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 20px;
            padding: 30px;
            border-left: 6px solid #ffc107;
            margin-top: 30px;
        }

        .explanation-panel h3 {
            color: #856404;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .step-explanation {
            display: none;
            color: #856404;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .step-explanation.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .experts-container {
                flex-wrap: wrap;
                gap: 15px;
            }

            .expert {
                width: 120px;
                height: 100px;
            }

            .moe-architecture {
                min-height: 500px;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🧠 DeepSeek MoE架构交互演示</h1>
            <p>体验混合专家模型如何智能选择最适合的专家来处理不同类型的任务</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🎯 输入您的问题，看看MoE如何工作</h3>
            <div class="input-section">
                <input type="text" class="input-box" id="userInput" placeholder="例如：写一首诗、解决数学问题、翻译文本、编写代码..."
                    value="帮我写一首关于春天的诗">
                <button class="process-btn" id="processBtn" onclick="processMoE()">
                    🚀 开始处理
                </button>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="loadRandomExample()"
                    style="padding: 10px 20px; background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 10px; cursor: pointer; font-weight: 600;">
                    🎲 随机示例
                </button>
                <button onclick="showMoEInfo()"
                    style="padding: 10px 20px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 10px; cursor: pointer; font-weight: 600; margin-left: 10px;">
                    ℹ️ 什么是MoE？
                </button>
            </div>
        </div>

        <!-- MoE架构可视化 -->
        <div class="moe-architecture">
            <div class="architecture-title">DeepSeek MoE 处理流程</div>

            <!-- 输入层 -->
            <div class="input-layer" id="inputLayer">
                📝 输入文本
            </div>

            <!-- 路由器 -->
            <div class="router" id="router">
                🎯<br>智能路由器
            </div>

            <!-- 专家模块 -->
            <div class="experts-container">
                <div class="expert" id="expert1" data-type="creative">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🎨</div>
                    <div class="expert-name">创意写作专家</div>
                </div>

                <div class="expert" id="expert2" data-type="math">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🔢</div>
                    <div class="expert-name">数学计算专家</div>
                </div>

                <div class="expert" id="expert3" data-type="code">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">💻</div>
                    <div class="expert-name">编程代码专家</div>
                </div>

                <div class="expert" id="expert4" data-type="language">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🌐</div>
                    <div class="expert-name">语言翻译专家</div>
                </div>

                <div class="expert" id="expert5" data-type="analysis">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">📊</div>
                    <div class="expert-name">数据分析专家</div>
                </div>
            </div>

            <!-- 输出层 -->
            <div class="output-layer" id="outputLayer">
                ✨ 处理结果
            </div>

            <!-- 连接线 -->
            <div class="connection-line" id="line1"
                style="top: 120px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
            <div class="connection-line" id="line2"
                style="top: 270px; left: 30%; width: 100px; transform: rotate(-30deg);"></div>
            <div class="connection-line" id="line3"
                style="top: 270px; left: 45%; width: 80px; transform: rotate(-15deg);"></div>
            <div class="connection-line" id="line4"
                style="top: 270px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
            <div class="connection-line" id="line5"
                style="top: 270px; right: 45%; width: 80px; transform: rotate(15deg);"></div>
            <div class="connection-line" id="line6"
                style="top: 270px; right: 30%; width: 100px; transform: rotate(30deg);"></div>
            <div class="connection-line" id="line7"
                style="bottom: 120px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
        </div>

        <!-- 说明面板 -->
        <div class="explanation-panel">
            <h3>📚 处理步骤说明</h3>

            <div class="step-explanation active" id="step0">
                <strong>🎯 准备阶段：</strong>输入您的问题，MoE系统将智能分析并选择最适合的专家来处理。
            </div>

            <div class="step-explanation" id="step1">
                <strong>📝 步骤1 - 输入分析：</strong>系统接收到您的输入文本，开始进行语义分析和任务类型识别。
            </div>

            <div class="step-explanation" id="step2">
                <strong>🎯 步骤2 - 智能路由：</strong>路由器根据输入内容的特征，计算每个专家的匹配度分数，决定激活哪些专家。
            </div>

            <div class="step-explanation" id="step3">
                <strong>🧠 步骤3 - 专家处理：</strong>被选中的专家开始并行处理任务，每个专家都有自己的专业领域和优势。
            </div>

            <div class="step-explanation" id="step4">
                <strong>⚡ 步骤4 - 结果融合：</strong>系统将各个专家的输出进行智能融合，生成最终的高质量结果。
            </div>
        </div>

        <script>
            // 专家类型和关键词映射
            const expertMapping = {
                creative: {
                    keywords: ['诗', '故事', '创作', '写作', '文学', '小说', '散文', '创意'],
                    name: '创意写作专家',
                    description: '擅长诗歌、故事、创意写作等文学创作任务'
                },
                math: {
                    keywords: ['计算', '数学', '方程', '公式', '求解', '算法', '几何', '代数'],
                    name: '数学计算专家',
                    description: '专门处理数学计算、方程求解、数值分析等任务'
                },
                code: {
                    keywords: ['代码', '编程', '函数', '算法', '开发', 'python', 'javascript', '程序'],
                    name: '编程代码专家',
                    description: '负责代码编写、程序设计、算法实现等编程任务'
                },
                language: {
                    keywords: ['翻译', '英语', '中文', '语言', '外语', '转换', '国际'],
                    name: '语言翻译专家',
                    description: '处理多语言翻译、语言转换、国际化等任务'
                },
                analysis: {
                    keywords: ['分析', '数据', '统计', '报告', '图表', '趋势', '研究'],
                    name: '数据分析专家',
                    description: '专注于数据分析、统计研究、趋势预测等任务'
                }
            };

            let currentStep = 0;
            let isProcessing = false;

            // 计算专家匹配度
            function calculateExpertScores(input) {
                const scores = {};
                const inputLower = input.toLowerCase();

                for (const [type, expert] of Object.entries(expertMapping)) {
                    let score = 0;
                    expert.keywords.forEach(keyword => {
                        if (inputLower.includes(keyword)) {
                            score += 20;
                        }
                    });

                    // 添加一些随机性，模拟真实的评分
                    score += Math.random() * 10;
                    scores[type] = Math.round(score);
                }

                return scores;
            }

            // 显示步骤说明
            function showStep(stepNumber) {
                document.querySelectorAll('.step-explanation').forEach(el => {
                    el.classList.remove('active');
                });

                const stepEl = document.getElementById(`step${stepNumber}`);
                if (stepEl) {
                    stepEl.classList.add('active');
                }
            }

            // 重置所有状态
            function resetStates() {
                document.querySelectorAll('.expert').forEach(expert => {
                    expert.classList.remove('active', 'selected', 'scored');
                    expert.querySelector('.expert-score').textContent = '0';
                });

                document.querySelectorAll('.connection-line').forEach(line => {
                    line.classList.remove('active');
                });

                document.getElementById('outputLayer').classList.remove('show');
                currentStep = 0;
                showStep(0);
            }

            // 主要的MoE处理函数
            async function processMoE() {
                if (isProcessing) return;

                isProcessing = true;
                const btn = document.getElementById('processBtn');
                const input = document.getElementById('userInput').value.trim();

                if (!input) {
                    alert('请输入一些文本！');
                    isProcessing = false;
                    return;
                }

                btn.disabled = true;
                btn.textContent = '🔄 处理中...';

                resetStates();

                // 更新输入层显示
                document.getElementById('inputLayer').textContent = `📝 "${input.substring(0, 20)}${input.length > 20 ? '...' : ''}"`;

                // 步骤1：输入分析
                currentStep = 1;
                showStep(1);
                await sleep(1500);

                // 步骤2：智能路由和评分
                currentStep = 2;
                showStep(2);
                document.getElementById('line1').classList.add('active');
                await sleep(1000);

                const scores = calculateExpertScores(input);

                // 显示专家评分
                for (const [type, score] of Object.entries(scores)) {
                    const expert = document.querySelector(`[data-type="${type}"]`);
                    if (expert) {
                        expert.classList.add('scored');
                        expert.querySelector('.expert-score').textContent = score;
                        await sleep(300);
                    }
                }

                await sleep(1000);

                // 步骤3：选择和激活专家
                currentStep = 3;
                showStep(3);

                // 选择得分最高的2-3个专家
                const sortedExperts = Object.entries(scores)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, Math.min(3, Object.keys(scores).length));

                // 激活选中的专家和连接线
                for (let i = 0; i < sortedExperts.length; i++) {
                    const [type] = sortedExperts[i];
                    const expert = document.querySelector(`[data-type="${type}"]`);
                    if (expert) {
                        expert.classList.add('selected');

                        // 激活对应的连接线
                        const expertIndex = Array.from(document.querySelectorAll('.expert')).indexOf(expert);
                        const lineId = `line${expertIndex + 2}`;
                        document.getElementById(lineId)?.classList.add('active');

                        await sleep(500);
                    }
                }

                await sleep(2000);

                // 步骤4：输出结果
                currentStep = 4;
                showStep(4);
                document.getElementById('line7').classList.add('active');
                await sleep(1000);

                // 显示输出
                const selectedExpertTypes = sortedExperts.map(([type]) => type);
                const outputText = generateOutput(input, selectedExpertTypes);
                document.getElementById('outputLayer').textContent = outputText;
                document.getElementById('outputLayer').classList.add('show');

                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = '🚀 开始处理';
                isProcessing = false;
            }

            // 生成输出文本
            function generateOutput(input, expertTypes) {
                const outputs = {
                    creative: '✨ 创意内容已生成',
                    math: '🔢 数学问题已解决',
                    code: '💻 代码已编写完成',
                    language: '🌐 翻译已完成',
                    analysis: '📊 分析报告已生成'
                };

                if (expertTypes.length > 0) {
                    return outputs[expertTypes[0]] || '✅ 处理完成';
                }
                return '✅ 通用处理完成';
            }

            // 工具函数：延时
            function sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            // 专家点击事件
            document.querySelectorAll('.expert').forEach(expert => {
                expert.addEventListener('click', function () {
                    const type = this.dataset.type;
                    const expertInfo = expertMapping[type];
                    alert(`${expertInfo.name}\n\n${expertInfo.description}`);
                });
            });

            // 回车键处理
            document.getElementById('userInput').addEventListener('keypress', function (e) {
                if (e.key === 'Enter' && !isProcessing) {
                    processMoE();
                }
            });

            // 预设示例
            const examples = [
                "帮我写一首关于春天的诗",
                "计算 2x + 5 = 15 的解",
                "用Python写一个排序算法",
                "把'Hello World'翻译成中文",
                "分析一下电商销售数据的趋势"
            ];

            // 随机示例按钮
            function loadRandomExample() {
                const randomExample = examples[Math.floor(Math.random() * examples.length)];
                document.getElementById('userInput').value = randomExample;
            }

            // 显示MoE信息
            function showMoEInfo() {
                const info = `🧠 什么是MoE（混合专家模型）？

MoE（Mixture of Experts）是一种创新的AI架构，它的核心思想是：

🎯 **智能分工**：不同的"专家"负责不同类型的任务
📊 **动态选择**：根据输入内容智能选择最合适的专家
⚡ **高效处理**：只激活需要的专家，节省计算资源
🚀 **性能提升**：专业化处理带来更好的结果质量

DeepSeek的MoE架构特点：
• 多个专业化的专家模块
• 智能路由器进行任务分配
• 稀疏激活机制提高效率
• 端到端的联合训练

这种架构让AI系统既保持了强大的能力，又提高了处理效率！`;

                alert(info);
            }

            // 增强专家交互
            function highlightExpertType(type) {
                document.querySelectorAll('.expert').forEach(expert => {
                    if (expert.dataset.type === type) {
                        expert.style.transform = 'scale(1.1)';
                        expert.style.boxShadow = '0 20px 60px rgba(0, 123, 255, 0.4)';
                    } else {
                        expert.style.transform = 'scale(1)';
                        expert.style.boxShadow = '';
                    }
                });
            }

            // 添加键盘快捷键
            document.addEventListener('keydown', function (e) {
                if (e.ctrlKey && e.key === 'Enter' && !isProcessing) {
                    processMoE();
                }
                if (e.key === 'Escape') {
                    resetStates();
                }
            });

            // 初始化
            document.addEventListener('DOMContentLoaded', function () {
                showStep(0);

                // 添加提示信息
                setTimeout(() => {
                    if (!isProcessing) {
                        document.getElementById('userInput').focus();
                    }
                }, 1000);
            });
        </script>
    </div>
</body>

</html>
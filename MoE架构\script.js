// 专家类型和关键词映射
const expertMapping = {
    creative: {
        keywords: ['诗', '故事', '创作', '写作', '文学', '小说', '散文', '创意', '诗歌', '文章'],
        name: '创意写作专家',
        description: '擅长诗歌、故事、创意写作等文学创作任务。专注于语言的艺术性和创造性表达。'
    },
    math: {
        keywords: ['计算', '数学', '方程', '公式', '求解', '算法', '几何', '代数', '数字', '运算'],
        name: '数学计算专家',
        description: '专门处理数学计算、方程求解、数值分析等任务。精通各种数学运算和逻辑推理。'
    },
    code: {
        keywords: ['代码', '编程', '函数', '算法', '开发', 'python', 'javascript', '程序', '软件', '编码'],
        name: '编程代码专家',
        description: '负责代码编写、程序设计、算法实现等编程任务。熟悉多种编程语言和开发框架。'
    },
    language: {
        keywords: ['翻译', '英语', '中文', '语言', '外语', '转换', '国际', '英文', '日语', '法语'],
        name: '语言翻译专家',
        description: '处理多语言翻译、语言转换、国际化等任务。精通多种语言的语法和文化背景。'
    },
    analysis: {
        keywords: ['分析', '数据', '统计', '报告', '图表', '趋势', '研究', '调查', '评估', '预测'],
        name: '数据分析专家',
        description: '专注于数据分析、统计研究、趋势预测等任务。擅长从数据中提取有价值的洞察。'
    }
};

// 预设示例
const examples = [
    "帮我写一首关于春天的诗",
    "计算 2x + 5 = 15 的解",
    "用Python写一个排序算法",
    "把'Hello World'翻译成中文",
    "分析一下电商销售数据的趋势",
    "写一个关于友谊的短故事",
    "解决这个几何问题：求圆的面积",
    "用JavaScript实现一个计时器",
    "将这段中文翻译成英文：你好世界",
    "分析用户行为数据的模式"
];

let currentStep = 0;
let isProcessing = false;

// 计算专家匹配度
function calculateExpertScores(input) {
    const scores = {};
    const inputLower = input.toLowerCase();

    for (const [type, expert] of Object.entries(expertMapping)) {
        let score = 0;
        expert.keywords.forEach(keyword => {
            if (inputLower.includes(keyword)) {
                score += 20;
            }
        });

        // 添加一些随机性，模拟真实的评分
        score += Math.random() * 15;
        scores[type] = Math.round(score);
    }

    return scores;
}

// 显示实时解释
function showLiveExplanation(stepNumber) {
    document.querySelectorAll('.explanation-item').forEach(el => {
        el.classList.remove('active');
    });

    const explanationEl = document.getElementById(`explanation${stepNumber}`);
    if (explanationEl) {
        explanationEl.classList.add('active');
    }
}

// 重置所有状态
function resetStates() {
    document.querySelectorAll('.expert').forEach(expert => {
        expert.classList.remove('active', 'selected', 'scored');
        expert.querySelector('.expert-score').textContent = '0';
        expert.style.transform = '';
        expert.style.boxShadow = '';
    });

    document.querySelectorAll('.connection-line').forEach(line => {
        line.classList.remove('active');
    });

    document.getElementById('outputLayer').classList.remove('show');
    document.getElementById('inputLayer').textContent = '📝 输入文本';
    document.getElementById('outputLayer').textContent = '✨ 处理结果';

    currentStep = 0;
    showLiveExplanation(0);
}

// 主要的MoE处理函数
async function processMoE() {
    if (isProcessing) return;

    isProcessing = true;
    const btn = document.getElementById('processBtn');
    const input = document.getElementById('userInput').value.trim();

    if (!input) {
        alert('请输入一些文本！');
        isProcessing = false;
        return;
    }

    btn.disabled = true;
    btn.textContent = '🔄 处理中...';

    resetStates();

    // 更新输入层显示
    const displayText = input.length > 15 ? input.substring(0, 15) + '...' : input;
    document.getElementById('inputLayer').textContent = `📝 "${displayText}"`;

    // 步骤1：输入分析
    currentStep = 1;
    showLiveExplanation(1);
    await sleep(2000);

    // 步骤2：智能路由和评分
    currentStep = 2;
    showLiveExplanation(2);
    document.getElementById('line1').classList.add('active');
    await sleep(1500);

    const scores = calculateExpertScores(input);

    // 显示专家评分（逐个显示）
    for (const [type, score] of Object.entries(scores)) {
        const expert = document.querySelector(`[data-type="${type}"]`);
        if (expert) {
            expert.classList.add('scored');
            expert.querySelector('.expert-score').textContent = score;

            // 添加评分动画效果
            expert.style.transform = 'scale(1.1)';
            setTimeout(() => {
                expert.style.transform = 'scale(1)';
            }, 300);

            await sleep(400);
        }
    }

    await sleep(1500);

    // 步骤3：选择和激活专家
    currentStep = 3;
    showLiveExplanation(3);

    // 选择得分最高的2-3个专家
    const sortedExperts = Object.entries(scores)
        .sort(([, a], [, b]) => b - a)
        .slice(0, Math.min(3, Object.keys(scores).length));

    // 激活选中的专家和连接线
    for (let i = 0; i < sortedExperts.length; i++) {
        const [type, score] = sortedExperts[i];
        const expert = document.querySelector(`[data-type="${type}"]`);
        if (expert && score > 5) { // 只激活得分较高的专家
            expert.classList.add('selected');

            // 激活对应的连接线
            const expertIndex = Array.from(document.querySelectorAll('.expert')).indexOf(expert);
            const lineId = `line${expertIndex + 2}`;
            document.getElementById(lineId)?.classList.add('active');

            await sleep(600);
        }
    }

    await sleep(2500);

    // 步骤4：输出结果
    currentStep = 4;
    showLiveExplanation(4);
    document.getElementById('line7').classList.add('active');
    await sleep(1200);

    // 显示输出
    const selectedExpertTypes = sortedExperts
        .filter(([, score]) => score > 5)
        .map(([type]) => type);
    const outputText = generateOutput(input, selectedExpertTypes);
    document.getElementById('outputLayer').textContent = outputText;
    document.getElementById('outputLayer').classList.add('show');

    await sleep(1000);

    // 恢复按钮状态
    btn.disabled = false;
    btn.textContent = '🚀 开始处理';
    isProcessing = false;

    // 显示完成提示
    setTimeout(() => {
        showLiveExplanation(0);
    }, 3000);
}

// 生成输出文本
function generateOutput(input, expertTypes) {
    const detailedOutputs = {
        creative: {
            icon: '✨',
            title: '创意内容已生成',
            details: '运用丰富的想象力和文学技巧，创作出富有感染力的内容。专家分析了语言的韵律、情感表达和艺术美感。'
        },
        math: {
            icon: '🔢',
            title: '数学问题已解决',
            details: '通过严密的逻辑推理和精确计算，得出准确答案。专家运用了代数、几何或统计学方法进行求解。'
        },
        code: {
            icon: '💻',
            title: '代码已编写完成',
            details: '遵循最佳编程实践，编写出高效、可维护的代码。专家考虑了算法复杂度、代码可读性和性能优化。'
        },
        language: {
            icon: '🌐',
            title: '翻译已完成',
            details: '准确传达原文含义，保持语言的自然流畅。专家考虑了文化背景、语法结构和表达习惯的差异。'
        },
        analysis: {
            icon: '📊',
            title: '分析报告已生成',
            details: '深入挖掘数据背后的规律和趋势，提供有价值的洞察。专家运用了统计分析、数据挖掘和可视化技术。'
        }
    };

    if (expertTypes.length > 0) {
        const primaryExpert = expertTypes[0];
        const expertCount = expertTypes.length;
        const output = detailedOutputs[primaryExpert];

        if (output) {
            if (expertCount > 1) {
                return `${output.icon} ${output.title}\n💡 ${output.details}\n🤝 本次处理由${expertCount}个专家协作完成，确保结果的全面性和准确性。`;
            } else {
                return `${output.icon} ${output.title}\n💡 ${output.details}`;
            }
        }
    }

    return '✅ 通用处理完成\n💡 系统已完成基础处理，为您提供标准化的结果输出。';
}

// 工具函数：延时
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 随机示例按钮
function loadRandomExample() {
    const randomExample = examples[Math.floor(Math.random() * examples.length)];
    document.getElementById('userInput').value = randomExample;

    // 添加视觉反馈
    const inputBox = document.getElementById('userInput');
    inputBox.style.background = '#e3f2fd';
    setTimeout(() => {
        inputBox.style.background = '';
    }, 500);
}

// 显示MoE信息
function showMoEInfo() {
    const info = `🧠 什么是MoE（混合专家模型）？

MoE（Mixture of Experts）是一种创新的AI架构，它的核心思想是：

🎯 **智能分工**：不同的"专家"负责不同类型的任务
📊 **动态选择**：根据输入内容智能选择最合适的专家  
⚡ **高效处理**：只激活需要的专家，节省计算资源
🚀 **性能提升**：专业化处理带来更好的结果质量

DeepSeek的MoE架构特点：
• 多个专业化的专家模块
• 智能路由器进行任务分配  
• 稀疏激活机制提高效率
• 端到端的联合训练

这种架构让AI系统既保持了强大的能力，又提高了处理效率！

💡 试试不同类型的问题，看看哪些专家会被激活！`;

    alert(info);
}

// 专家点击事件
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.expert').forEach(expert => {
        expert.addEventListener('click', function () {
            const type = this.dataset.type;
            const expertInfo = expertMapping[type];

            // 创建更美观的提示框
            const message = `${expertInfo.name}\n\n${expertInfo.description}\n\n💡 试试输入相关的问题，看看这个专家是否会被激活！`;
            alert(message);
        });

        // 添加悬停效果
        expert.addEventListener('mouseenter', function () {
            if (!this.classList.contains('selected') && !isProcessing) {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            }
        });

        expert.addEventListener('mouseleave', function () {
            if (!this.classList.contains('selected') && !isProcessing) {
                this.style.transform = '';
                this.style.boxShadow = '';
            }
        });
    });

    // 回车键处理
    document.getElementById('userInput').addEventListener('keypress', function (e) {
        if (e.key === 'Enter' && !isProcessing) {
            processMoE();
        }
    });

    // 添加键盘快捷键
    document.addEventListener('keydown', function (e) {
        if (e.ctrlKey && e.key === 'Enter' && !isProcessing) {
            processMoE();
        }
        if (e.key === 'Escape') {
            resetStates();
        }
        if (e.key === 'r' && e.ctrlKey) {
            e.preventDefault();
            loadRandomExample();
        }
    });

    // 初始化
    showLiveExplanation(0);

    // 添加提示信息
    setTimeout(() => {
        if (!isProcessing) {
            document.getElementById('userInput').focus();
        }
    }, 1000);

    // 添加页面加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 增强的专家交互功能
function highlightExpertType(type) {
    document.querySelectorAll('.expert').forEach(expert => {
        if (expert.dataset.type === type) {
            expert.style.transform = 'scale(1.1)';
            expert.style.boxShadow = '0 20px 60px rgba(0, 123, 255, 0.4)';
            expert.style.borderColor = '#007bff';
        } else {
            expert.style.transform = 'scale(0.95)';
            expert.style.opacity = '0.6';
        }
    });

    // 3秒后恢复
    setTimeout(() => {
        document.querySelectorAll('.expert').forEach(expert => {
            expert.style.transform = '';
            expert.style.boxShadow = '';
            expert.style.opacity = '';
            expert.style.borderColor = '';
        });
    }, 3000);
}

// 添加一些有趣的交互效果
function addSparkleEffect(element) {
    const sparkle = document.createElement('div');
    sparkle.innerHTML = '✨';
    sparkle.style.position = 'absolute';
    sparkle.style.pointerEvents = 'none';
    sparkle.style.fontSize = '1.5em';
    sparkle.style.animation = 'sparkle 1s ease-out forwards';

    const rect = element.getBoundingClientRect();
    sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
    sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';

    document.body.appendChild(sparkle);

    setTimeout(() => {
        sparkle.remove();
    }, 1000);
}

// 添加CSS动画（如果需要）
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkle {
        0% {
            opacity: 1;
            transform: scale(0) rotate(0deg);
        }
        50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
        }
        100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
        }
    }
`;
document.head.appendChild(style);

// 导出函数供全局使用
window.processMoE = processMoE;
window.loadRandomExample = loadRandomExample;
window.showMoEInfo = showMoEInfo;
window.highlightExpertType = highlightExpertType;

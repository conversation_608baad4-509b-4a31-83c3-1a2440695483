 * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
 }

 body {
     font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
     min-height: 100vh;
     color: #333;
 }

 .container {
     max-width: 1200px;
     margin: 0 auto;
     padding: 20px;
 }

 .header {
     text-align: center;
     margin-bottom: 40px;
     color: white;
 }

 .header h1 {
     font-size: 2.5em;
     margin-bottom: 10px;
     text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
 }

 .header p {
     font-size: 1.2em;
     opacity: 0.9;
 }

 /* 神经网络模块样式 */
 .neural-network-section {
     margin-bottom: 60px;
 }

 .neural-network-section .header {
     margin-bottom: 30px;
 }

 .intro-text {
     background: rgba(255, 255, 255, 0.1);
     border-radius: 15px;
     padding: 25px;
     margin: 20px 0;
     backdrop-filter: blur(10px);
     border: 1px solid rgba(255, 255, 255, 0.2);
 }

 .intro-text p {
     color: rgba(255, 255, 255, 0.95);
     line-height: 1.8;
     font-size: 1.1em;
     margin-bottom: 15px;
 }

 .intro-text p:last-child {
     margin-bottom: 0;
 }

 .neural-demo-container {
     text-align: center;
     margin: 30px 0;
 }

 .main-demo-btn {
     background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
     color: white;
     border: none;
     padding: 20px 40px;
     font-size: 1.3em;
     border-radius: 50px;
     cursor: pointer;
     transition: all 0.3s ease;
     box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
     font-weight: bold;
 }

 .main-demo-btn:hover {
     transform: translateY(-3px);
     box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
     background: linear-gradient(45deg, #FF5252, #26C6DA);
 }

 /* 分隔线样式 */
 .section-divider {
     display: flex;
     align-items: center;
     margin: 50px 0;
     opacity: 0.7;
 }

 .divider-line {
     flex: 1;
     height: 2px;
     background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
 }

 .divider-text {
     color: white;
     padding: 0 20px;
     font-size: 1.1em;
     font-weight: bold;
     text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
 }

 .modules-grid {
     display: grid;
     grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
     gap: 30px;
     margin-bottom: 40px;
 }

 .module {
     background: white;
     border-radius: 15px;
     padding: 25px;
     box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
     transition: transform 0.3s ease, box-shadow 0.3s ease;
     position: relative;
     overflow: hidden;
 }

 .module:hover {
     transform: translateY(-5px);
     box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
 }

 .module::before {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     height: 4px;
     background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
 }

 .module h3 {
     color: #2c3e50;
     margin-bottom: 15px;
     font-size: 1.4em;
     display: flex;
     align-items: center;
 }

 .module-number {
     background: linear-gradient(45deg, #667eea, #764ba2);
     color: white;
     width: 30px;
     height: 30px;
     border-radius: 50%;
     display: flex;
     align-items: center;
     justify-content: center;
     margin-right: 10px;
     font-weight: bold;
 }

 .module p {
     line-height: 1.6;
     margin-bottom: 20px;
     color: #555;
 }

 .demo-btn {
     background: linear-gradient(45deg, #667eea, #764ba2);
     color: white;
     border: none;
     padding: 12px 25px;
     border-radius: 25px;
     cursor: pointer;
     font-size: 1em;
     transition: all 0.3s ease;
     width: 100%;
 }

 .demo-btn:hover {
     transform: scale(1.05);
     box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
 }

 .demo-modal {
     display: none;
     position: fixed;
     z-index: 1000;
     left: 0;
     top: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(0, 0, 0, 0.8);
     backdrop-filter: blur(5px);
 }

 .modal-content {
     background-color: white;
     margin: 2% auto;
     padding: 30px;
     border-radius: 15px;
     width: 90%;
     max-width: 800px;
     max-height: 90vh;
     overflow-y: auto;
     position: relative;
 }

 .close {
     color: #aaa;
     float: right;
     font-size: 28px;
     font-weight: bold;
     cursor: pointer;
     position: absolute;
     right: 20px;
     top: 15px;
 }

 .close:hover {
     color: #000;
 }

 .demo-area {
     margin-top: 20px;
     padding: 20px;
     background: #f8f9fa;
     border-radius: 10px;
     border: 2px dashed #ddd;
 }

 .input-sentence {
     width: 100%;
     padding: 15px;
     border: 2px solid #ddd;
     border-radius: 8px;
     font-size: 1.1em;
     margin-bottom: 15px;
 }

 .process-btn {
     background: #28a745;
     color: white;
     border: none;
     padding: 12px 25px;
     border-radius: 8px;
     cursor: pointer;
     font-size: 1em;
     margin-right: 10px;
 }

 .result-area {
     margin-top: 20px;
     padding: 15px;
     background: white;
     border-radius: 8px;
     border: 1px solid #ddd;
     min-height: 100px;
 }

 .token {
     display: inline-block;
     background: #e3f2fd;
     border: 1px solid #2196f3;
     padding: 5px 10px;
     margin: 3px;
     border-radius: 15px;
     font-size: 0.9em;
 }

 .attention-matrix {
     display: grid;
     gap: 2px;
     margin: 10px 0;
 }

 .attention-cell {
     width: 30px;
     height: 30px;
     display: flex;
     align-items: center;
     justify-content: center;
     border-radius: 4px;
     font-size: 0.8em;
     color: white;
     font-weight: bold;
 }

 @keyframes fadeIn {
     from {
         opacity: 0;
         transform: translateY(20px);
     }

     to {
         opacity: 1;
         transform: translateY(0);
     }
 }

 .fade-in {
     animation: fadeIn 0.5s ease-out;
 }

 .progress-indicator {
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 4px;
     background: rgba(255, 255, 255, 0.2);
     z-index: 1001;
 }

 .progress-bar {
     height: 100%;
     background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
     width: 0%;
     transition: width 0.3s ease;
 }

 .floating-help {
     position: fixed;
     bottom: 30px;
     right: 30px;
     background: #667eea;
     color: white;
     width: 60px;
     height: 60px;
     border-radius: 50%;
     display: flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
     box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
     transition: all 0.3s ease;
     z-index: 1000;
 }

 .floating-help:hover {
     transform: scale(1.1);
     box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
 }

 .help-tooltip {
     position: absolute;
     bottom: 70px;
     right: 0;
     background: white;
     color: #333;
     padding: 15px;
     border-radius: 10px;
     box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
     width: 250px;
     display: none;
     font-size: 0.9em;
 }

 .help-tooltip::after {
     content: '';
     position: absolute;
     top: 100%;
     right: 20px;
     border: 8px solid transparent;
     border-top-color: white;
 }

 .demo-controls {
     display: flex;
     gap: 10px;
     margin-bottom: 15px;
     flex-wrap: wrap;
 }

 .control-btn {
     background: #6c757d;
     color: white;
     border: none;
     padding: 8px 15px;
     border-radius: 5px;
     cursor: pointer;
     font-size: 0.9em;
     transition: background 0.3s ease;
 }

 .control-btn:hover {
     background: #5a6268;
 }

 .control-btn.active {
     background: #007bff;
 }

 .flow-step {
     background: rgba(255, 255, 255, 0.2);
     color: white;
     padding: 8px 15px;
     border-radius: 20px;
     font-size: 0.9em;
     font-weight: bold;
     border: 2px solid rgba(255, 255, 255, 0.3);
     transition: all 0.3s ease;
 }

 .flow-step:hover {
     background: rgba(255, 255, 255, 0.3);
     transform: scale(1.05);
 }

 .flow-arrow {
     color: white;
     font-size: 1.2em;
     font-weight: bold;
 }

 .purpose-section {
     background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
     border-left: 4px solid #2196f3;
     padding: 15px;
     margin: 15px 0;
     border-radius: 8px;
 }

 .purpose-title {
     color: #1976d2;
     font-weight: bold;
     margin-bottom: 8px;
     display: flex;
     align-items: center;
 }

 .connection-section {
     background: linear-gradient(135deg, #fff3e0, #ffeaa7);
     border-left: 4px solid #ff9800;
     padding: 15px;
     margin: 15px 0;
     border-radius: 8px;
 }

 .connection-title {
     color: #f57c00;
     font-weight: bold;
     margin-bottom: 8px;
     display: flex;
     align-items: center;
 }

 .analogy-section {
     background: linear-gradient(135deg, #f3e5f5, #e1bee7);
     border-left: 4px solid #9c27b0;
     padding: 15px;
     margin: 15px 0;
     border-radius: 8px;
 }

 .analogy-title {
     color: #7b1fa2;
     font-weight: bold;
     margin-bottom: 8px;
     display: flex;
     align-items: center;
 }

 .collapsible {
     background: #f8f9fa;
     color: #333;
     cursor: pointer;
     padding: 12px;
     width: 100%;
     border: none;
     text-align: left;
     outline: none;
     font-size: 0.9em;
     border-radius: 8px;
     margin: 8px 0;
     transition: background-color 0.3s ease;
     display: flex;
     justify-content: space-between;
     align-items: center;
 }

 .collapsible:hover {
     background: #e9ecef;
 }

 .collapsible.active {
     background: #e3f2fd;
 }

 .collapsible-content {
     padding: 0;
     max-height: 0;
     overflow: hidden;
     transition: max-height 0.3s ease-out, padding 0.3s ease-out;
     background: white;
     border-radius: 0 0 8px 8px;
 }

 .collapsible-content.active {
     max-height: 1000px;
     padding: 15px;
 }

 .chevron {
     transition: transform 0.3s ease;
     font-size: 1.2em;
 }

 .chevron.active {
     transform: rotate(180deg);
 }

 /* Transformer流程演示样式 */
 .transformer-flow-section {
     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
     border-radius: 20px;
     padding: 40px;
     margin: 40px 0;
     color: white;
 }

 .flow-header {
     text-align: center;
     margin-bottom: 30px;
 }

 .flow-header h2 {
     font-size: 2.2em;
     margin-bottom: 10px;
     text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
 }

 .flow-header p {
     font-size: 1.1em;
     opacity: 0.9;
 }

 .flow-input-area {
     background: rgba(255, 255, 255, 0.1);
     border-radius: 15px;
     padding: 25px;
     margin-bottom: 30px;
     backdrop-filter: blur(10px);
 }

 .input-controls {
     display: flex;
     gap: 15px;
     align-items: center;
     flex-wrap: wrap;
     justify-content: center;
 }

 .flow-input {
     flex: 1;
     min-width: 300px;
     padding: 12px 20px;
     border: none;
     border-radius: 25px;
     font-size: 1.1em;
     background: rgba(255, 255, 255, 0.9);
     color: #333;
     outline: none;
     transition: all 0.3s ease;
 }

 .flow-input:focus {
     background: white;
     box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
 }

 .flow-btn {
     padding: 12px 25px;
     border: none;
     border-radius: 25px;
     font-size: 1em;
     font-weight: bold;
     cursor: pointer;
     transition: all 0.3s ease;
     min-width: 120px;
 }

 .flow-btn.primary {
     background: linear-gradient(45deg, #4CAF50, #45a049);
     color: white;
 }

 .flow-btn.secondary {
     background: linear-gradient(45deg, #ff6b6b, #ee5a52);
     color: white;
 }

 .flow-btn:hover {
     transform: translateY(-2px);
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
 }

 .flow-steps-container {
     display: grid;
     grid-template-columns: repeat(3, 1fr);
     gap: 25px;
     margin-bottom: 30px;
     max-width: 1400px;
     margin-left: auto;
     margin-right: auto;
 }

 /* 演示区域的流程步骤样式 */
 .transformer-flow-section .flow-step {
     background: rgba(255, 255, 255, 0.1);
     border-radius: 15px;
     padding: 25px;
     backdrop-filter: blur(10px);
     border: 2px solid transparent;
     transition: all 0.5s ease;
     opacity: 0.6;
     min-height: 200px;
 }



 .transformer-flow-section .flow-step.active {
     opacity: 1;
     border-color: #FFD700;
     background: rgba(255, 215, 0, 0.2);
     transform: scale(1.02);
     box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
 }

 .transformer-flow-section .flow-step.completed {
     opacity: 1;
     border-color: #4CAF50;
     background: rgba(76, 175, 80, 0.2);
 }

 .step-header {
     display: flex;
     align-items: center;
     gap: 15px;
     margin-bottom: 15px;
 }

 .step-number {
     width: 40px;
     /* 恢复原始大小 */
     height: 40px;
     /* 恢复原始大小 */
     border-radius: 50%;
     background: linear-gradient(45deg, #FFD700, #FFA500);
     color: #333;
     display: flex;
     align-items: center;
     justify-content: center;
     font-weight: bold;
     font-size: 1.2em;
     /* 恢复原始大小 */
 }

 /* 限制大尺寸样式只应用于流程演示区域 */
 .transformer-flow-section .step-number {
     width: 50px;
     height: 50px;
     font-size: 1.4em;
 }

 .transformer-flow-section .step-header h3 {
     font-size: 1.5em;
 }

 .transformer-flow-section .step-content {
     font-size: 1.05em;
     min-height: 120px;
 }

 .transformer-flow-section .flow-step.active .step-number {
     animation: pulse 2s infinite;
 }

 .transformer-flow-section .flow-step.completed .step-number {
     background: linear-gradient(45deg, #4CAF50, #45a049);
     color: white;
 }

 .step-header h3 {
     flex: 1;
     margin: 0;
     font-size: 1.3em;
     /* 恢复原始大小 */
 }

 .step-status {
     padding: 5px 12px;
     border-radius: 15px;
     font-size: 0.9em;
     font-weight: bold;
     background: rgba(255, 255, 255, 0.2);
 }

 .transformer-flow-section .flow-step.active .step-status {
     background: rgba(255, 215, 0, 0.8);
     color: #333;
 }

 .transformer-flow-section .flow-step.completed .step-status {
     background: rgba(76, 175, 80, 0.8);
     color: white;
 }

 .step-content {
     line-height: 1.6;
     min-height: 100px;
     /* 恢复原始大小 */
     font-size: 1em;
     /* 恢复原始大小 */
 }

 .flow-result {
     background: rgba(255, 255, 255, 0.1);
     border-radius: 15px;
     padding: 25px;
     backdrop-filter: blur(10px);
     text-align: center;
 }

 .flow-result h3 {
     margin-bottom: 15px;
     color: #FFD700;
 }

 .result-content {
     font-size: 1.1em;
     line-height: 1.6;
     min-height: 60px;
     display: flex;
     align-items: center;
     justify-content: center;
 }

 /* 响应式设计更新 */
 @media (max-width: 1200px) {
     .flow-steps-container {
         grid-template-columns: repeat(2, 1fr);
         max-width: 900px;
     }
 }

 @media (max-width: 768px) {
     .transformer-flow-section {
         padding: 20px;
         margin: 20px 0;
     }

     .flow-steps-container {
         grid-template-columns: 1fr;
         max-width: 100%;
     }

     .input-controls {
         flex-direction: column;
     }

     .flow-input {
         min-width: 100%;
     }

     .flow-header h2 {
         font-size: 1.8em;
     }

     .step-number {
         width: 45px;
         height: 45px;
         font-size: 1.3em;
     }

     .step-header h3 {
         font-size: 1.3em;
     }

     .step-content {
         font-size: 1em;
         min-height: 100px;
     }

     .transformer-flow-section .flow-step {
         padding: 20px;
         min-height: 180px;
     }
 }

 /* 核心代码相关样式 */
 .code-section {
     margin-top: 15px;
     padding-top: 15px;
     border-top: 1px solid rgba(255, 255, 255, 0.1);
 }

 .code-btn {
     background: linear-gradient(45deg, #6c5ce7, #a29bfe);
     color: white;
     border: none;
     padding: 10px 20px;
     border-radius: 8px;
     font-size: 0.9em;
     font-weight: bold;
     cursor: pointer;
     transition: all 0.3s ease;
     display: flex;
     align-items: center;
     gap: 8px;
     margin: 0 auto;
     box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
 }

 .code-btn:hover {
     background: linear-gradient(45deg, #5f3dc4, #9775fa);
     transform: translateY(-2px);
     box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
 }

 /* 核心代码弹窗样式 */
 .code-modal {
     display: none;
     position: fixed;
     z-index: 2000;
     left: 0;
     top: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(0, 0, 0, 0.8);
     backdrop-filter: blur(5px);
 }

 .code-modal-content {
     background: linear-gradient(135deg, #1e3c72, #2a5298);
     margin: 2% auto;
     border-radius: 15px;
     width: 90%;
     max-width: 1200px;
     max-height: 90vh;
     overflow: hidden;
     box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
     animation: modalSlideIn 0.3s ease-out;
 }

 @keyframes modalSlideIn {
     from {
         opacity: 0;
         transform: translateY(-50px) scale(0.9);
     }

     to {
         opacity: 1;
         transform: translateY(0) scale(1);
     }
 }

 .code-modal-header {
     background: linear-gradient(45deg, #667eea, #764ba2);
     color: white;
     padding: 20px 25px;
     display: flex;
     justify-content: space-between;
     align-items: center;
     border-bottom: 2px solid rgba(255, 255, 255, 0.1);
 }

 .code-modal-header h3 {
     margin: 0;
     font-size: 1.4em;
     font-weight: bold;
 }

 .code-modal-close {
     font-size: 28px;
     font-weight: bold;
     cursor: pointer;
     color: rgba(255, 255, 255, 0.8);
     transition: all 0.3s ease;
     width: 35px;
     height: 35px;
     display: flex;
     align-items: center;
     justify-content: center;
     border-radius: 50%;
     background: rgba(255, 255, 255, 0.1);
 }

 .code-modal-close:hover {
     color: white;
     background: rgba(255, 255, 255, 0.2);
     transform: scale(1.1);
 }

 .code-modal-body {
     padding: 25px;
     max-height: calc(90vh - 100px);
     overflow-y: auto;
 }

 .code-description {
     color: rgba(255, 255, 255, 0.9);
     font-size: 1.1em;
     margin-bottom: 20px;
     padding: 15px;
     background: rgba(255, 255, 255, 0.1);
     border-radius: 10px;
     border-left: 4px solid #6c5ce7;
 }

 .code-container {
     background: #1a1a1a;
     border-radius: 12px;
     overflow: hidden;
     box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
 }

 .code-header {
     background: linear-gradient(45deg, #2d3748, #4a5568);
     color: white;
     padding: 12px 20px;
     display: flex;
     justify-content: space-between;
     align-items: center;
     border-bottom: 1px solid #4a5568;
 }

 .code-language {
     font-weight: bold;
     color: #ffd700;
     font-size: 0.9em;
 }

 .copy-code-btn {
     background: linear-gradient(45deg, #48bb78, #38a169);
     color: white;
     border: none;
     padding: 6px 12px;
     border-radius: 6px;
     font-size: 0.8em;
     cursor: pointer;
     transition: all 0.3s ease;
 }

 .copy-code-btn:hover {
     background: linear-gradient(45deg, #38a169, #2f855a);
     transform: scale(1.05);
 }

 .code-content {
     background: #1a1a1a;
     color: #e2e8f0;
     padding: 20px;
     margin: 0;
     font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
     font-size: 0.9em;
     line-height: 1.6;
     overflow-x: auto;
     white-space: pre-wrap;
     word-wrap: break-word;
 }

 /* 代码语法高亮 */
 .code-content .comment {
     color: #68d391;
     font-style: italic;
 }

 .code-content .keyword {
     color: #f56565;
     font-weight: bold;
 }

 .code-content .string {
     color: #fbb6ce;
 }

 .code-content .number {
     color: #90cdf4;
 }

 /* 响应式设计 */
 @media (max-width: 768px) {
     .code-modal-content {
         width: 95%;
         margin: 5% auto;
         max-height: 85vh;
     }

     .code-modal-header {
         padding: 15px 20px;
     }

     .code-modal-header h3 {
         font-size: 1.2em;
     }

     .code-modal-body {
         padding: 20px;
     }

     .code-content {
         font-size: 0.8em;
         padding: 15px;
     }

     .code-btn {
         padding: 8px 16px;
         font-size: 0.8em;
     }
 }

 /* 神经网络核心代码按钮样式 */
 .neural-code-btn {
     background: linear-gradient(45deg, #9b59b6, #e74c3c);
     color: white;
     border: none;
     padding: 10px 18px;
     border-radius: 8px;
     font-size: 0.9em;
     font-weight: bold;
     cursor: pointer;
     transition: all 0.3s ease;
     margin-left: 10px;
     box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
 }

 .neural-code-btn:hover {
     background: linear-gradient(45deg, #8e44ad, #c0392b);
     transform: translateY(-2px);
     box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
 }

 /* 响应式设计 - 神经网络按钮 */
 @media (max-width: 768px) {
     .neural-code-btn {
         padding: 8px 14px;
         font-size: 0.8em;
         margin-left: 5px;
     }
 }
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.2em;
    color: #718096;
    font-weight: 300;
}

/* 控制面板 */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.control-panel h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.controls button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
}

.btn-reset {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #8b0000;
}

.btn-toggle {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2d3748;
}

.controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.controls button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.speed-control label {
    font-weight: bold;
    color: #4a5568;
}

#speed-slider {
    width: 150px;
}

#speed-value {
    font-weight: bold;
    color: #667eea;
    min-width: 30px;
}

/* 架构说明 */
.architecture-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.architecture-info h2 {
    font-size: 1.4em;
    margin-bottom: 10px;
}

.architecture-info p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 架构容器 */
.architecture-container {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    min-height: 800px;
}

.transformer-architecture {
    width: 100%;
    height: 100%;
}

.architecture-layout {
    display: grid;
    grid-template-columns: 250px 1fr 250px;
    grid-template-rows: auto 1fr auto;
    gap: 30px;
    min-height: 700px;
    position: relative;
    align-items: start;
    justify-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 输入部分 - 左上角 */
.input-section {
    grid-column: 1;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    justify-self: center;
    align-self: start;
}

.input-text,
.target-text {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border: 2px solid #4fd1c7;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    min-width: 150px;
}

.input-label {
    font-weight: bold;
    color: #2c7a7b;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.input-tokens {
    display: flex;
    gap: 5px;
    justify-content: center;
    flex-wrap: wrap;
}

.token {
    background: #4fd1c7;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

/* 嵌入层 */
.embedding-layer {
    background: linear-gradient(135deg, #fed7e2 0%, #fbb6ce 100%);
    border: 2px solid #ed64a6;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    min-width: 150px;
    position: relative;
}

.layer-title {
    font-weight: bold;
    color: #97266d;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.layer-subtitle {
    color: #b83280;
    font-size: 0.8em;
    font-style: italic;
}

/* 主体架构 - 中间位置 */
.main-architecture {
    grid-column: 2;
    grid-row: 1 / 4;
    display: flex;
    gap: 50px;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
}

/* 编码器和解码器部分 */
.encoder-section,
.decoder-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 200px;
}

.encoder-section {
    border: 3px dashed #e53e3e;
    border-radius: 12px;
    padding: 20px;
    background: rgba(254, 215, 215, 0.3);
    position: relative;
    min-width: 280px;
    max-width: 350px;
}

.decoder-section {
    border: 3px dashed #3182ce;
    border-radius: 12px;
    padding: 20px;
    background: rgba(190, 227, 248, 0.3);
    position: relative;
    min-width: 280px;
    max-width: 350px;
}

.section-label {
    font-weight: bold;
    font-size: 1.1em;
    text-align: center;
    padding: 8px;
    border-radius: 8px;
    margin-bottom: 5px;
}

.encoder-label {
    background: #e53e3e;
    color: white;
}

.decoder-label {
    background: #3182ce;
    color: white;
}

.section-note {
    text-align: center;
    font-size: 0.8em;
    color: #666;
    margin-bottom: 15px;
}

/* 位置编码 */
.positional-encoding {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
    border: 2px solid #f6ad55;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 10px;
}

.pos-circle {
    width: 30px;
    height: 30px;
    background: #f6ad55;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2em;
}

.pos-label {
    font-size: 0.8em;
    font-weight: bold;
    color: #c05621;
    text-align: center;
    line-height: 1.2;
}

/* 编码器/解码器层样式 */
.encoder-stack,
.decoder-stack {
    margin-bottom: 40px;
}

.layer-label {
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    color: #4a5568;
    margin-bottom: 20px;
    padding: 10px;
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border-radius: 8px;
}

/* 编码器和解码器层 */
.encoder-layer,
.decoder-layer {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 15px;
    border: 2px solid #e2e8f0;
}

/* 注意力块 */
.attention-block {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border: 2px solid #4fd1c7;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    position: relative;
}

.attention-block.masked {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-color: #fc8181;
}

.attention-block.cross {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border-color: #68d391;
}

/* 块标题和副标题 */
.block-title {
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.block-subtitle {
    color: #718096;
    font-size: 0.7em;
    font-style: italic;
    margin-bottom: 8px;
}

/* 注意力头 */
.attention-heads {
    display: flex;
    gap: 5px;
    justify-content: center;
    margin-bottom: 8px;
}

.head {
    background: #4fd1c7;
    color: white;
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: bold;
}

/* 掩码指示器 */
.mask-indicator {
    background: #fc8181;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 5px;
}

/* 交叉连接 */
.cross-connection {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 5px;
}

.query-line,
.kv-line {
    background: rgba(255, 255, 255, 0.8);
    padding: 3px 8px;
    border-radius: 5px;
    font-size: 0.7em;
    font-weight: bold;
}

.query-line {
    color: #3182ce;
}

.kv-line {
    color: #38a169;
}

/* 连接箭头 */
.connection-arrow {
    position: absolute;
    right: -35px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2em;
    color: #667eea;
    font-weight: bold;
    animation: pulse 2s ease-in-out infinite;
    z-index: 5;
}

/* Add & Norm 块 */
.add-norm-block {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border: 2px solid #cbd5e0;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.residual-arrow {
    font-size: 1.2em;
    color: #48bb78;
    font-weight: bold;
}

.norm-box {
    text-align: center;
    flex: 1;
}

.norm-title {
    font-weight: bold;
    color: #4a5568;
    font-size: 0.8em;
    margin-bottom: 2px;
}

.norm-subtitle {
    color: #718096;
    font-size: 0.7em;
}

/* 前馈网络块 */
.ffn-block {
    background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
    border: 2px solid #f6ad55;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.ffn-structure {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
}

.ffn-layer {
    background: #f6ad55;
    color: white;
    padding: 4px 8px;
    border-radius: 5px;
    font-size: 0.7em;
    font-weight: bold;
}

.ffn-activation {
    background: #ed8936;
    color: white;
    padding: 4px 8px;
    border-radius: 5px;
    font-size: 0.7em;
    font-weight: bold;
}

/* 线性层部分 - 右上角 */
.linear-section {
    grid-column: 3;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    justify-self: center;
    align-self: start;
}

/* 输出部分 - 右下角 */
.output-section {
    grid-column: 3;
    grid-row: 3;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    justify-self: center;
    align-self: end;
}

/* 目标输入部分 - 左下角 */
.target-input-section {
    grid-column: 1;
    grid-row: 3;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    justify-self: center;
    align-self: end;
}

.linear-layer,
.softmax-layer {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border: 2px solid #4fd1c7;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    min-width: 150px;
}

.output-probs {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 10px;
}

.prob-item {
    background: #4fd1c7;
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.8em;
    font-weight: bold;
}

/* 箭头 */
.arrow-down,
.arrow-up {
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    margin: 8px auto;
    position: relative;
}

.arrow-down {
    border-top: 18px solid #667eea;
}

.arrow-up {
    border-bottom: 18px solid #667eea;
}

.arrow-down::after,
.arrow-up::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 20px;
    background: #667eea;
    left: 50%;
    transform: translateX(-50%);
}

.arrow-down::after {
    top: -25px;
}

.arrow-up::after {
    bottom: -25px;
}

/* 添加数据流动箭头 */
.flow-arrow {
    position: absolute;
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, transparent, #667eea, transparent);
    animation: flowDown 2s ease-in-out infinite;
}

@keyframes flowDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }

    50% {
        opacity: 1;
        transform: translateY(0);
    }

    100% {
        opacity: 0;
        transform: translateY(10px);
    }
}

/* 知识提示 */
.knowledge-tooltip {
    background: #f0fff4;
    border-left: 4px solid #48bb78;
    padding: 8px 12px;
    margin-top: 8px;
    border-radius: 0 6px 6px 0;
    font-size: 0.75em;
    color: #2f855a;
    line-height: 1.4;
    display: none;
}

/* 悬停显示知识提示 */
.attention-block:hover .knowledge-tooltip,
.add-norm-block:hover .knowledge-tooltip,
.ffn-block:hover .knowledge-tooltip,
.embedding-layer:hover .knowledge-tooltip,
.linear-layer:hover .knowledge-tooltip,
.softmax-layer:hover .knowledge-tooltip,
.positional-encoding:hover .knowledge-tooltip {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* 激活状态 */
.attention-block.active,
.add-norm-block.active,
.ffn-block.active,
.embedding-layer.active,
.linear-layer.active,
.softmax-layer.active,
.positional-encoding.active {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    z-index: 10;
}

.attention-block.active .knowledge-tooltip,
.add-norm-block.active .knowledge-tooltip,
.ffn-block.active .knowledge-tooltip,
.embedding-layer.active .knowledge-tooltip,
.linear-layer.active .knowledge-tooltip,
.softmax-layer.active .knowledge-tooltip,
.positional-encoding.active .knowledge-tooltip {
    display: block;
}

/* 前馈网络样式 */
.ffn-structure {
    text-align: center;
    margin-bottom: 15px;
}

.ffn-layer {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    display: inline-block;
}

/* 掩码注意力样式 */
.mask-visualization {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.mask-matrix {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 5px;
    background: #f7fafc;
    padding: 10px;
    border-radius: 8px;
}

.mask-cell {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-weight: bold;
}

.mask-cell.visible {
    background: #48bb78;
    color: white;
}

.mask-cell.hidden {
    background: #fed7d7;
    color: #c53030;
}

/* 交叉注意力样式 */
.attention-flow {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.query,
.key-value {
    background: #f0f8ff;
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
}

.query {
    color: #2b6cb0;
    border-left: 4px solid #4299e1;
}

.key-value {
    color: #2f855a;
    border-left: 4px solid #48bb78;
}

/* 更多层指示 */
.more-layers {
    text-align: center;
    padding: 20px;
    color: #718096;
    font-style: italic;
}

.dots {
    font-size: 2em;
    color: #cbd5e0;
    margin: 10px 0;
}

/* 输出层样式 */
.output-section {
    text-align: center;
    margin-top: 40px;
}

.output-box {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border: 2px solid #48bb78;
    border-radius: 15px;
    padding: 25px;
    display: inline-block;
    min-width: 300px;
}

.output-box h4 {
    color: #2f855a;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.context-vectors,
.output-probs {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

.vector,
.prob {
    background: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: bold;
    color: #2f855a;
    border: 1px solid #48bb78;
}

/* 知识点总结 */
.knowledge-summary {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.knowledge-summary h2 {
    color: #2d3748;
    margin-bottom: 25px;
    text-align: center;
    font-size: 1.8em;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.summary-card {
    background: #f7fafc;
    padding: 20px;
    border-radius: 12px;
    border-left: 5px solid #667eea;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
}

.summary-card h3 {
    color: #4a5568;
    margin-bottom: 10px;
}

.summary-card p {
    color: #718096;
    line-height: 1.6;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes flowAnimation {
    0% {
        transform: translateY(0);
        opacity: 1;
    }

    50% {
        transform: translateY(-10px);
        opacity: 0.7;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.component.animating {
    animation: pulse 1s ease-in-out;
}

.data-flow.animating .token {
    animation: flowAnimation 1s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2em;
    }

    .architecture-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto auto;
        gap: 20px;
        padding: 10px;
    }

    .input-section {
        grid-column: 1;
        grid-row: 1;
    }

    .linear-section {
        grid-column: 1;
        grid-row: 2;
    }

    .main-architecture {
        grid-column: 1;
        grid-row: 3;
        flex-direction: column;
        gap: 30px;
    }

    .output-section {
        grid-column: 1;
        grid-row: 4;
    }

    .target-input-section {
        grid-column: 1;
        grid-row: 5;
    }

    .encoder-section,
    .decoder-section {
        min-width: auto;
        max-width: none;
        width: 100%;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }
}
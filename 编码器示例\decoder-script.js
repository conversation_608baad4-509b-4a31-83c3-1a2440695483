// 解码器演示脚本
class DecoderDemo {
    constructor() {
        this.currentStep = 0;
        this.totalSteps = 7;
        this.isRunning = false;

        // 步骤数据
        this.steps = [
            {
                id: 'target-embedding',
                title: '步骤1: 目标输入嵌入 + 位置编码',
                description: '将目标序列转换为向量表示，并添加位置信息',
                knowledge: `
                    <h4>🎯 目标输入嵌入（Target Embedding）</h4>
                    <p><strong>简单理解：</strong>就像给每个目标词汇分配一个"身份证号码"</p>
                    <div class="example-box">
                        <p><strong>举例：</strong>翻译任务中，如果要翻译成"我 爱 你"</p>
                        <ul>
                            <li>"我" → [0.3, -0.2, 0.8, ...]（512维向量）</li>
                            <li>"爱" → [0.1, 0.7, -0.4, ...]（512维向量）</li>
                            <li>"你" → [-0.2, 0.5, 0.9, ...]（512维向量）</li>
                        </ul>
                    </div>
                    
                    <h4>📍 位置编码的重要性</h4>
                    <p><strong>为什么需要？</strong>因为解码器需要知道当前生成到第几个词了</p>
                    <div class="example-box">
                        <p><strong>举例：</strong>生成"我爱你"时</p>
                        <ul>
                            <li>位置1的"我" + 位置编码[1] = 知道这是第一个词</li>
                            <li>位置2的"爱" + 位置编码[2] = 知道这是第二个词</li>
                            <li>位置3的"你" + 位置编码[3] = 知道这是第三个词</li>
                        </ul>
                    </div>
                `,
                visualization: this.createTargetEmbeddingVisualization
            },
            {
                id: 'masked-attention',
                title: '步骤2: 掩码多头自注意力机制',
                description: '只能看到当前位置之前的信息，防止"偷看"未来',
                knowledge: `
                    <h4>🎭 掩码机制（Masking）</h4>
                    <p><strong>核心思想：</strong>生成第N个词时，只能看到前N-1个词，不能"偷看"后面的词</p>
                    <div class="example-box">
                        <p><strong>生活类比：</strong>就像考试时不能偷看后面的答案</p>
                        <p><strong>翻译例子：</strong>生成"我爱你"时</p>
                        <ul>
                            <li>生成"我"时：什么都看不到（开始标记除外）</li>
                            <li>生成"爱"时：只能看到"我"</li>
                            <li>生成"你"时：只能看到"我爱"</li>
                        </ul>
                    </div>
                    
                    <h4>🧠 多头注意力的作用</h4>
                    <p><strong>简单理解：</strong>用多个"大脑"同时思考不同方面的信息</p>
                    <div class="example-box">
                        <ul>
                            <li><strong>头1：</strong>关注语法结构（主谓宾关系）</li>
                            <li><strong>头2：</strong>关注语义关系（词汇含义）</li>
                            <li><strong>头3：</strong>关注上下文连贯性</li>
                        </ul>
                    </div>
                `,
                visualization: this.createMaskedAttentionVisualization
            },
            {
                id: 'addnorm1',
                title: '步骤3: 残差连接 + 层归一化',
                description: '保持信息流畅，稳定训练过程',
                knowledge: `
                    <h4>🔄 残差连接（Residual Connection）</h4>
                    <p><strong>生活类比：</strong>就像走路时保持平衡，既要前进也要保持稳定</p>
                    <div class="example-box">
                        <p><strong>数学表示：</strong>输出 = 注意力结果 + 原始输入</p>
                        <p><strong>作用：</strong>防止信息在深层网络中"丢失"</p>
                        <ul>
                            <li>原始信息：[1.0, 2.0, 3.0]</li>
                            <li>注意力输出：[0.2, -0.1, 0.5]</li>
                            <li>最终结果：[1.2, 1.9, 3.5]</li>
                        </ul>
                    </div>
                    
                    <h4>⚖️ 层归一化（Layer Normalization）</h4>
                    <p><strong>简单理解：</strong>让数据保持在合理范围内，就像调节音量</p>
                    <div class="example-box">
                        <p><strong>作用：</strong>防止数值过大或过小，让训练更稳定</p>
                        <p><strong>类比：</strong>就像自动调节音响音量，保持听觉舒适</p>
                    </div>
                `,
                visualization: this.createAddNormVisualization
            },
            {
                id: 'cross-attention',
                title: '步骤4: 编码器-解码器注意力机制',
                description: '解码器"询问"编码器：我现在应该关注输入的哪部分？',
                knowledge: `
                    <h4>🤝 编码器-解码器注意力</h4>
                    <p><strong>核心思想：</strong>解码器向编码器"提问"，获取相关信息</p>
                    <div class="example-box">
                        <p><strong>翻译类比：</strong>中译英时</p>
                        <ul>
                            <li><strong>编码器：</strong>理解了"我爱你"的含义</li>
                            <li><strong>解码器生成"I"时：</strong>问编码器"我"对应什么？</li>
                            <li><strong>解码器生成"love"时：</strong>问编码器"爱"对应什么？</li>
                            <li><strong>解码器生成"you"时：</strong>问编码器"你"对应什么？</li>
                        </ul>
                    </div>
                    
                    <h4>🔍 注意力机制详解</h4>
                    <div class="example-box">
                        <p><strong>Query（查询）：</strong>解码器当前状态（我想知道什么？）</p>
                        <p><strong>Key（键）：</strong>编码器的每个位置（有哪些信息可查？）</p>
                        <p><strong>Value（值）：</strong>编码器的具体信息（实际的信息内容）</p>
                    </div>
                `,
                visualization: this.createCrossAttentionVisualization
            },
            {
                id: 'addnorm2',
                title: '步骤5: 残差连接 + 层归一化',
                description: '再次稳定信息流，为下一步做准备',
                knowledge: `
                    <h4>🔄 第二次残差连接</h4>
                    <p><strong>为什么又来一次？</strong>因为刚刚融合了编码器信息，需要再次稳定</p>
                    <div class="example-box">
                        <p><strong>类比：</strong>就像做菜时，加了新调料后要再次搅拌均匀</p>
                        <ul>
                            <li>解码器原有信息 + 编码器提供的信息 = 更丰富的表示</li>
                            <li>通过残差连接保持信息完整性</li>
                            <li>通过层归一化保持数值稳定性</li>
                        </ul>
                    </div>
                    
                    <h4>🎯 信息融合的重要性</h4>
                    <div class="example-box">
                        <p><strong>翻译例子：</strong>生成英文"love"时</p>
                        <ul>
                            <li>解码器知道：现在要生成第2个英文单词</li>
                            <li>编码器提供：中文"爱"的语义信息</li>
                            <li>融合结果：在第2个位置生成表达"爱"含义的英文词</li>
                        </ul>
                    </div>
                `,
                visualization: this.createAddNorm2Visualization
            },
            {
                id: 'ffn',
                title: '步骤6: 前馈神经网络',
                description: '对融合后的信息进行深度处理和变换',
                knowledge: `
                    <h4>🧠 前馈神经网络（FFN）</h4>
                    <p><strong>简单理解：</strong>就像大脑的"深度思考"过程</p>
                    <div class="example-box">
                        <p><strong>网络结构：</strong>输入层 → 隐藏层 → 输出层</p>
                        <ul>
                            <li><strong>输入层：</strong>512维（融合后的信息）</li>
                            <li><strong>隐藏层：</strong>2048维（扩展思考空间）</li>
                            <li><strong>输出层：</strong>512维（精炼后的结果）</li>
                        </ul>
                    </div>
                    
                    <h4>🔄 信息处理过程</h4>
                    <div class="example-box">
                        <p><strong>类比：</strong>就像写作文时的思考过程</p>
                        <ul>
                            <li><strong>第一步：</strong>收集所有相关信息（输入）</li>
                            <li><strong>第二步：</strong>在脑海中展开思考（隐藏层处理）</li>
                            <li><strong>第三步：</strong>整理成最终的表达（输出）</li>
                        </ul>
                    </div>
                    
                    <h4>⚡ ReLU激活函数</h4>
                    <p><strong>作用：</strong>增加非线性，让模型能处理复杂关系</p>
                `,
                visualization: this.createFFNVisualization
            },
            {
                id: 'addnorm3',
                title: '步骤7: 残差连接 + 层归一化',
                description: '最终稳定输出，准备生成下一个词',
                knowledge: `
                    <h4>🏁 最终的稳定化处理</h4>
                    <p><strong>为什么需要第三次？</strong>经过FFN深度处理后，需要最后一次稳定</p>
                    <div class="example-box">
                        <p><strong>类比：</strong>就像写完文章后的最后检查和润色</p>
                        <ul>
                            <li>确保信息没有丢失（残差连接）</li>
                            <li>确保输出格式标准（层归一化）</li>
                            <li>为下一个词的生成做好准备</li>
                        </ul>
                    </div>
                    
                    <h4>🔄 解码器层的完整流程</h4>
                    <div class="example-box">
                        <p><strong>一个完整的解码器层包含：</strong></p>
                        <ol>
                            <li>掩码自注意力（看历史）</li>
                            <li>残差连接 + 层归一化</li>
                            <li>编码器-解码器注意力（问编码器）</li>
                            <li>残差连接 + 层归一化</li>
                            <li>前馈神经网络（深度思考）</li>
                            <li>残差连接 + 层归一化（最终稳定）</li>
                        </ol>
                    </div>
                    
                    <h4>🎯 输出到词汇表</h4>
                    <p><strong>最后一步：</strong>将处理好的向量转换为具体的词汇</p>
                    <div class="example-box">
                        <p>通过线性层和softmax，从词汇表中选择概率最高的词</p>
                    </div>
                `,
                visualization: this.createFinalVisualization
            }
        ];

        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        this.startBtn = document.getElementById('startDemo');
        this.resetBtn = document.getElementById('resetDemo');
        this.nextBtn = document.getElementById('nextStep');
        this.prevBtn = document.getElementById('prevStep');
        this.progressFill = document.getElementById('progressFill');
        this.stepCounter = document.getElementById('stepCounter');
        this.stepTitle = document.getElementById('stepTitle');
        this.stepDescription = document.getElementById('stepDescription');
        this.stepVisualization = document.getElementById('stepVisualization');
        this.knowledgeContent = document.getElementById('knowledgeContent');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startDemo());
        this.resetBtn.addEventListener('click', () => this.resetDemo());
        this.nextBtn.addEventListener('click', () => this.nextStep());
        this.prevBtn.addEventListener('click', () => this.prevStep());

        // 组件点击事件
        document.querySelectorAll('.component').forEach((component, index) => {
            component.addEventListener('click', () => this.jumpToStep(index + 1));
        });
    }

    startDemo() {
        this.isRunning = true;
        this.currentStep = 1;
        this.updateUI();
        this.showStep(1);

        this.startBtn.disabled = true;
        this.nextBtn.disabled = false;
        this.prevBtn.disabled = true;
    }

    resetDemo() {
        this.isRunning = false;
        this.currentStep = 0;
        this.updateUI();

        // 重置所有组件状态
        document.querySelectorAll('.component').forEach(comp => {
            comp.classList.remove('active');
        });

        this.stepTitle.textContent = '点击"开始演示"开始学习';
        this.stepDescription.innerHTML = `
            <p>欢迎来到Transformer解码器流程演示！</p>
            <p>本演示将带您逐步了解解码器的每个组件及其工作原理。</p>
        `;
        this.stepVisualization.innerHTML = '';
        this.knowledgeContent.innerHTML = `
            <div class="welcome-knowledge">
                <h4>🎯 学习目标</h4>
                <ul>
                    <li>理解Transformer解码器的完整工作流程</li>
                    <li>掌握掩码注意力机制的作用原理</li>
                    <li>学会分析编码器-解码器注意力的工作方式</li>
                    <li>了解解码器如何生成目标序列</li>
                </ul>
                <h4>📚 使用说明</h4>
                <p>点击"开始演示"按钮，系统将逐步引导您学习解码器的每个组件。解码器负责根据编码器的输出生成目标序列。</p>
                <h4>🔗 相关链接</h4>
                <p><a href="index.html" style="color: #4CAF50; text-decoration: none;">← 返回编码器演示</a></p>
            </div>
        `;

        this.startBtn.disabled = false;
        this.nextBtn.disabled = true;
        this.prevBtn.disabled = true;
    }

    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.updateUI();
            this.showStep(this.currentStep);
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateUI();
            this.showStep(this.currentStep);
        }
    }

    jumpToStep(step) {
        if (this.isRunning && step >= 1 && step <= this.totalSteps) {
            this.currentStep = step;
            this.updateUI();
            this.showStep(step);
        }
    }

    updateUI() {
        // 更新进度条
        const progress = (this.currentStep / this.totalSteps) * 100;
        this.progressFill.style.width = `${progress}%`;

        // 更新步骤计数器
        this.stepCounter.textContent = `步骤 ${this.currentStep}/${this.totalSteps}`;

        // 更新按钮状态
        this.prevBtn.disabled = this.currentStep <= 1;
        this.nextBtn.disabled = this.currentStep >= this.totalSteps;

        // 更新组件高亮
        document.querySelectorAll('.component').forEach((comp, index) => {
            comp.classList.remove('active');
            if (index + 1 === this.currentStep) {
                comp.classList.add('active');
            }
        });
    }

    showStep(stepNumber) {
        const step = this.steps[stepNumber - 1];

        // 更新标题和描述
        this.stepTitle.textContent = step.title;
        this.stepDescription.innerHTML = `<p>${step.description}</p>`;

        // 更新知识点内容
        this.knowledgeContent.innerHTML = step.knowledge;

        // 创建可视化
        step.visualization.call(this);

        // 添加淡入动画
        this.stepVisualization.classList.add('fade-in');
        setTimeout(() => {
            this.stepVisualization.classList.remove('fade-in');
        }, 500);
    }

    // 可视化函数
    createTargetEmbeddingVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>目标序列嵌入过程</h4>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 10px; border-radius: 8px;">
                            <strong>目标词汇</strong><br>
                            ["&lt;start&gt;", "I", "love"]
                        </div>
                        <div style="font-size: 24px;">→</div>
                        <div style="background: #e1f5fe; padding: 10px; border-radius: 8px;">
                            <strong>嵌入向量</strong><br>
                            [0.1, 0.8, -0.3, ...]<br>
                            [0.4, -0.2, 0.7, ...]<br>
                            [-0.1, 0.5, 0.9, ...]
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>位置编码添加</h4>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px;">
                        <p><strong>解码器特点：</strong>自回归生成，一个词一个词地产生</p>
                        <div style="display: flex; justify-content: space-around; margin: 15px 0;">
                            <div style="background: white; padding: 10px; border-radius: 6px; border: 2px solid #9C27B0;">
                                <strong>位置1</strong><br>
                                &lt;start&gt; + pos[1]
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px; border: 2px solid #9C27B0;">
                                <strong>位置2</strong><br>
                                "I" + pos[2]
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px; border: 2px solid #9C27B0;">
                                <strong>位置3</strong><br>
                                "love" + pos[3]
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createMaskedAttentionVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>掩码注意力矩阵</h4>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>掩码规则：</strong>下三角矩阵，只能看到当前位置及之前的信息</p>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 5px; max-width: 300px; margin: 0 auto;">
                        <div style="background: #9C27B0; color: white; padding: 8px; text-align: center; border-radius: 4px;">0.8</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.3</div>
                        <div style="background: #9C27B0; color: white; padding: 8px; text-align: center; border-radius: 4px;">0.7</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.2</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.4</div>
                        <div style="background: #9C27B0; color: white; padding: 8px; text-align: center; border-radius: 4px;">0.4</div>
                        <div style="background: #ccc; padding: 8px; text-align: center; border-radius: 4px;">-∞</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.1</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.2</div>
                        <div style="background: #e1bee7; padding: 8px; text-align: center; border-radius: 4px;">0.3</div>
                        <div style="background: #9C27B0; color: white; padding: 8px; text-align: center; border-radius: 4px;">0.4</div>
                    </div>
                    <p style="margin-top: 10px; font-size: 14px; color: #666;">
                        紫色：可见权重，灰色：被掩码遮挡（-∞）
                    </p>
                </div>
            </div>
        `;
    }

    createAddNormVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>残差连接示例</h4>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <strong>原始输入</strong><br>
                            [1.2, 0.8, 2.1]
                        </div>
                        <div style="font-size: 20px;">+</div>
                        <div style="background: #e1f5fe; padding: 15px; border-radius: 8px;">
                            <strong>掩码注意力输出</strong><br>
                            [0.3, -0.2, 0.4]
                        </div>
                        <div style="font-size: 20px;">=</div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                            <strong>残差结果</strong><br>
                            [1.5, 0.6, 2.5]
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>层归一化效果</h4>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                        <p><strong>归一化前：</strong>[1.5, 0.6, 2.5] （数值差异较大）</p>
                        <p><strong>归一化后：</strong>[0.2, -0.8, 0.6] （数值范围稳定）</p>
                        <p style="font-size: 14px; color: #666; margin-top: 10px;">
                            让每层的输入都保持在合理范围内，训练更稳定
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    createCrossAttentionVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>编码器-解码器注意力机制</h4>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>核心思想：</strong>解码器向编码器"提问"获取相关信息</p>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div style="background: #e1f5fe; padding: 15px; border-radius: 8px;">
                            <h5>编码器输出（Key & Value）</h5>
                            <div style="margin: 10px 0;">
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">"我" → [0.2, 0.8, ...]</div>
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">"爱" → [0.5, -0.3, ...]</div>
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">"你" → [-0.1, 0.7, ...]</div>
                            </div>
                        </div>
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <h5>解码器查询（Query）</h5>
                            <div style="margin: 10px 0;">
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">生成"love"时的查询</div>
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">→ 关注"爱"(0.8权重)</div>
                                <div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px;">→ 获取对应语义信息</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>注意力权重分布</h4>
                    <div style="display: flex; justify-content: space-around; margin: 15px 0;">
                        <div style="background: #ffcdd2; padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>"我"</strong><br>0.1
                        </div>
                        <div style="background: #f8bbd9; padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>"爱"</strong><br>0.8
                        </div>
                        <div style="background: #ffcdd2; padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>"你"</strong><br>0.1
                        </div>
                    </div>
                    <p style="font-size: 14px; color: #666;">生成"love"时主要关注中文"爱"</p>
                </div>
            </div>
        `;
    }

    createAddNorm2Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>信息融合过程</h4>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <strong>解码器状态</strong><br>
                            [0.8, 1.2, 0.5]<br>
                            <small>（当前生成状态）</small>
                        </div>
                        <div style="font-size: 20px;">+</div>
                        <div style="background: #e1f5fe; padding: 15px; border-radius: 8px;">
                            <strong>编码器信息</strong><br>
                            [0.3, -0.4, 0.7]<br>
                            <small>（来自"爱"的语义）</small>
                        </div>
                        <div style="font-size: 20px;">=</div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                            <strong>融合结果</strong><br>
                            [1.1, 0.8, 1.2]<br>
                            <small>（丰富的表示）</small>
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>为什么需要第二次Add&Norm？</h4>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                        <p><strong>原因：</strong>刚刚融合了来自编码器的新信息</p>
                        <ul style="text-align: left; margin: 10px 0;">
                            <li>需要稳定融合后的表示</li>
                            <li>为下一步FFN处理做准备</li>
                            <li>保持信息流的连续性</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    createFFNVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>前馈神经网络处理</h4>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; border: 2px solid #9C27B0;">
                            <strong>输入层</strong><br>
                            512维<br>
                            融合后的信息
                        </div>
                        <div style="font-size: 20px;">→</div>
                        <div style="background: #e1f5fe; padding: 15px; border-radius: 8px; border: 2px solid #2196F3;">
                            <strong>隐藏层</strong><br>
                            2048维<br>
                            扩展思考空间
                        </div>
                        <div style="font-size: 20px;">→</div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <strong>输出层</strong><br>
                            512维<br>
                            精炼后结果
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>处理过程类比</h4>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin: 15px 0;">
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>收集信息</strong><br>
                                <small>整合所有相关信息</small>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>深度思考</strong><br>
                                <small>在更大空间中处理</small>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>得出结论</strong><br>
                                <small>形成最终表示</small>
                            </div>
                        </div>
                        <p style="font-size: 14px; color: #666;">
                            就像人类思考问题：收集信息 → 深度分析 → 得出结论
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    createFinalVisualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>解码器完整流程总结</h4>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 10px; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 10px 15px; border-radius: 8px; border: 2px solid #9C27B0; width: 280px;">
                            1. 目标嵌入 + 位置编码
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #e1f5fe; padding: 10px 15px; border-radius: 8px; border: 2px solid #2196F3; width: 280px;">
                            2. 掩码自注意力（看历史）
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #e8f5e8; padding: 10px 15px; border-radius: 8px; border: 2px solid #4CAF50; width: 280px;">
                            3. Add & Norm
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #fff3e0; padding: 10px 15px; border-radius: 8px; border: 2px solid #FF9800; width: 280px;">
                            4. 编码器-解码器注意力
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #fce4ec; padding: 10px 15px; border-radius: 8px; border: 2px solid #E91E63; width: 280px;">
                            5. Add & Norm
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #f1f8e9; padding: 10px 15px; border-radius: 8px; border: 2px solid #8BC34A; width: 280px;">
                            6. 前馈神经网络
                        </div>
                        <div style="font-size: 16px;">↓</div>
                        <div style="background: #f3e5f5; padding: 10px 15px; border-radius: 8px; border: 2px solid #9C27B0; width: 280px;">
                            7. Add & Norm → 输出
                        </div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <h4>解码器的核心特点</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <strong>🎭 掩码机制</strong><br>
                            <span style="font-size: 14px;">只能看历史，不能偷看未来</span>
                        </div>
                        <div style="background: #e1f5fe; padding: 15px; border-radius: 8px;">
                            <strong>🤝 跨注意力</strong><br>
                            <span style="font-size: 14px;">向编码器询问相关信息</span>
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                            <strong>🔄 自回归</strong><br>
                            <span style="font-size: 14px;">一个词一个词地生成</span>
                        </div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 8px;">
                            <strong>🎯 目标导向</strong><br>
                            <span style="font-size: 14px;">根据任务生成目标序列</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// 初始化演示
document.addEventListener('DOMContentLoaded', () => {
    new DecoderDemo();
});

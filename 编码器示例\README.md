# Transformer编码器流程演示

## 项目简介

这是一个交互式的Transformer编码器流程演示项目，通过可视化的方式帮助理解编码器的每个组件及其工作原理。

## 功能特点

### 🎯 核心功能
- **步骤式演示**：逐步展示编码器的5个主要步骤
- **交互式控制**：支持前进、后退、跳转到指定步骤
- **可视化展示**：每个步骤都有详细的图形化说明
- **知识点解析**：提供深入的理论知识讲解
- **整体架构图**：展示编码器的完整结构

### 📚 演示内容

#### 步骤1：输入嵌入 + 位置编码
- **词嵌入过程**：将离散词汇转换为连续向量
- **位置编码**：为序列添加位置信息
- **知识点**：
  - 嵌入向量的作用和原理
  - 位置编码的必要性
  - 正弦余弦位置编码公式

#### 步骤2：多头自注意力机制
- **注意力权重矩阵**：可视化注意力分布
- **多头机制**：展示不同注意力头的作用
- **知识点**：
  - Query、Key、Value的概念
  - 注意力计算公式
  - 多头注意力的优势

#### 步骤3：残差连接 + 层归一化
- **残差连接**：展示输入与输出的相加过程
- **层归一化**：说明归一化的作用
- **知识点**：
  - 残差连接解决梯度消失问题
  - 层归一化的计算公式
  - 训练稳定性的重要性

#### 步骤4：前馈神经网络
- **网络结构**：展示两层全连接网络
- **维度变化**：512 → 2048 → 512
- **知识点**：
  - FFN的数学表示
  - ReLU激活函数的作用
  - 非线性变换的意义

#### 步骤5：残差连接 + 层归一化
- **重复应用**：再次使用残差连接和层归一化
- **完整流程**：展示整个编码器层的处理过程
- **知识点**：
  - 编码器层的完整结构
  - 各组件的协同作用
  - Transformer的核心优势

## 使用方法

### 基本操作
1. **开始演示**：点击"开始演示"按钮开始学习
2. **步骤控制**：
   - 使用"下一步"/"上一步"按钮逐步学习
   - 点击架构图中的组件直接跳转到对应步骤
3. **重置**：点击"重置"按钮回到初始状态

### 界面说明
- **控制面板**：包含所有操作按钮和进度显示
- **架构概览**：显示编码器的整体结构，当前步骤会高亮显示
- **步骤详情**：展示当前步骤的详细说明和可视化内容
- **知识点详解**：提供深入的理论知识和公式解释
- **输入示例**：显示示例文本的分词结果

## 技术实现

### 文件结构
```
编码器示例/
├── index.html      # 主页面文件
├── style.css       # 样式文件
├── script.js       # 交互逻辑
└── README.md       # 说明文档
```

### 技术特点
- **纯前端实现**：使用HTML、CSS、JavaScript
- **响应式设计**：支持不同屏幕尺寸
- **动画效果**：平滑的过渡和高亮效果
- **模块化代码**：清晰的类结构和函数组织

## 教学价值

### 适用对象
- 深度学习初学者
- NLP研究人员
- 机器学习工程师
- 计算机科学学生

### 学习收获
- 深入理解Transformer编码器的工作原理
- 掌握各个组件的作用和相互关系
- 通过可视化加深对抽象概念的理解
- 为进一步学习Transformer架构打下基础

## 扩展建议

### 可能的改进方向
1. **添加数学公式动画**：展示计算过程
2. **支持自定义输入**：允许用户输入不同的文本
3. **添加参数调节**：可视化不同参数对结果的影响
4. **多语言支持**：支持英文等其他语言
5. **移动端优化**：改善移动设备上的体验

### 相关资源
- [Attention Is All You Need](https://arxiv.org/abs/1706.03762) - Transformer原论文
- [The Illustrated Transformer](http://jalammar.github.io/illustrated-transformer/) - 图解Transformer
- [Transformer模型详解](https://zhuanlan.zhihu.com/p/338817680) - 中文详解

## 版权说明

本项目仅用于教学和学习目的，欢迎在教育场景中使用和改进。

// 简化的JavaScript - 只保留交互功能

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    addInteractiveEffects();
});

// 添加交互效果
function addInteractiveEffects() {
    // 为所有组件添加悬停效果
    const components = document.querySelectorAll('.attention-block, .add-norm-block, .ffn-block, .embedding-layer, .linear-layer, .softmax-layer, .positional-encoding');
    
    components.forEach(component => {
        component.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.3)';
            this.style.zIndex = '10';
            this.style.transition = 'all 0.3s ease';
            
            const tooltip = this.querySelector('.knowledge-tooltip');
            if (tooltip) {
                tooltip.style.display = 'block';
            }
        });
        
        component.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            this.style.zIndex = '';
            
            const tooltip = this.querySelector('.knowledge-tooltip');
            if (tooltip) {
                tooltip.style.display = 'none';
            }
        });
        
        // 添加点击效果显示详细信息
        component.addEventListener('click', function() {
            showComponentDetails(this);
        });
    });
}

// 显示组件详细信息
function showComponentDetails(component) {
    const title = component.querySelector('.block-title, .layer-title')?.textContent || '组件详情';
    const description = component.querySelector('.knowledge-tooltip')?.textContent || '暂无详细信息';
    
    // 创建模态框显示详细信息
    const modal = document.createElement('div');
    modal.className = 'detail-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>${description}</p>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 添加关闭事件
    modal.querySelector('.close-btn').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    // 添加模态框样式
    if (!document.querySelector('#modal-styles')) {
        const modalStyles = document.createElement('style');
        modalStyles.id = 'modal-styles';
        modalStyles.textContent = `
            .detail-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .modal-header h3 {
                margin: 0;
                color: #2d3748;
            }
            
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #718096;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
            }
            
            .close-btn:hover {
                background: #f7fafc;
                color: #2d3748;
            }
            
            .modal-body {
                padding: 20px;
                line-height: 1.6;
                color: #4a5568;
            }
        `;
        document.head.appendChild(modalStyles);
    }
}

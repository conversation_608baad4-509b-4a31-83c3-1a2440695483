// 神经网络与深度学习模块JavaScript代码

// 神经网络演示状态
let currentNeuralStep = 'neuron';
let animationRunning = false;
let animationInterval = null;

// 神经网络演示数据
const neuralSteps = {
    neuron: {
        title: "🧠 神经元基础",
        description: `
            <strong>什么是人工神经元？</strong><br>
            人工神经元是模拟生物神经元的数学模型。它接收多个输入信号，对这些信号进行加权求和，然后通过激活函数产生输出。<br><br>
            
            <strong>核心组成部分：</strong><br>
            • <strong>输入(Input)：</strong>来自其他神经元或外部数据的信号<br>
            • <strong>权重(Weight)：</strong>决定每个输入的重要程度<br>
            • <strong>偏置(Bias)：</strong>调节神经元的激活阈值<br>
            • <strong>激活函数：</strong>决定神经元是否被激活以及激活程度<br><br>
            
            <strong>数学表达：</strong><br>
            输出 = 激活函数(∑(输入 × 权重) + 偏置)<br><br>
            
            <strong>生活类比：</strong><br>
            想象神经元像一个"决策者"，它收集来自不同渠道的信息（输入），根据每个信息的可信度（权重）进行综合考虑，最后做出决定（输出）。
        `,
        visualization: "neuron"
    },
    perceptron: {
        title: "⚡ 感知器模型",
        description: `
            <strong>感知器是什么？</strong><br>
            感知器是最简单的神经网络模型，由单个神经元组成。它能够学习线性可分的模式，是神经网络发展的重要里程碑。<br><br>
            
            <strong>工作原理：</strong><br>
            1. 接收输入数据（如图像的像素值）<br>
            2. 计算加权和：∑(输入 × 权重) + 偏置<br>
            3. 通过阶跃函数判断：结果>0输出1，否则输出0<br>
            4. 根据错误调整权重（学习过程）<br><br>
            
            <strong>学习过程：</strong><br>
            • 如果预测正确，权重不变<br>
            • 如果预测错误，调整权重使下次预测更准确<br>
            • 重复这个过程直到模型收敛<br><br>
            
            <strong>局限性：</strong><br>
            感知器只能解决线性可分问题，无法处理XOR等非线性问题。这个局限推动了多层网络的发展。
        `,
        visualization: "perceptron"
    },
    multilayer: {
        title: "🏗️ 多层神经网络",
        description: `
            <strong>为什么需要多层？</strong><br>
            单层感知器无法解决非线性问题。通过添加隐藏层，神经网络获得了处理复杂模式的能力。<br><br>
            
            <strong>网络结构：</strong><br>
            • <strong>输入层：</strong>接收原始数据<br>
            • <strong>隐藏层：</strong>提取和组合特征（可以有多层）<br>
            • <strong>输出层：</strong>产生最终预测结果<br><br>
            
            <strong>信息流动：</strong><br>
            数据从输入层开始，逐层向前传播。每一层都对前一层的输出进行变换，逐步提取更高级的特征。<br><br>
            
            <strong>非线性能力：</strong><br>
            通过使用非线性激活函数（如ReLU、Sigmoid），多层网络能够学习复杂的非线性映射关系。<br><br>
            
            <strong>特征层次：</strong><br>
            • 第一层：学习简单特征（边缘、纹理）<br>
            • 中间层：组合简单特征形成复杂特征<br>
            • 最后层：基于高级特征做出决策
        `,
        visualization: "multilayer"
    },
    backprop: {
        title: "🔄 反向传播算法",
        description: `
            <strong>学习的核心机制</strong><br>
            反向传播是训练多层神经网络的关键算法。它通过计算梯度来调整网络中的权重，使网络能够从错误中学习。<br><br>
            
            <strong>算法步骤：</strong><br>
            1. <strong>前向传播：</strong>输入数据通过网络产生预测<br>
            2. <strong>计算损失：</strong>比较预测值与真实值的差异<br>
            3. <strong>反向传播：</strong>从输出层向输入层传播误差<br>
            4. <strong>更新权重：</strong>根据梯度调整每个连接的权重<br><br>
            
            <strong>梯度下降：</strong><br>
            想象在山坡上找最低点，梯度告诉我们最陡的下坡方向。我们沿着这个方向小步前进，最终到达山谷底部（最优解）。<br><br>
            
            <strong>链式法则：</strong><br>
            反向传播使用微积分的链式法则，将复杂网络的梯度计算分解为简单的局部计算。<br><br>
            
            <strong>为什么重要：</strong><br>
            没有反向传播，深度网络就无法有效训练。它是现代AI发展的基础算法。
        `,
        visualization: "backprop"
    },
    deep: {
        title: "🌊 深度学习",
        description: `
            <strong>深度的力量</strong><br>
            深度学习通过构建具有多个隐藏层的神经网络，能够自动学习数据的层次化表示，无需人工设计特征。<br><br>
            
            <strong>深度网络的优势：</strong><br>
            • <strong>自动特征学习：</strong>无需手工设计特征<br>
            • <strong>层次化表示：</strong>从简单到复杂的特征组合<br>
            • <strong>强大表达能力：</strong>能够逼近任意复杂函数<br>
            • <strong>端到端学习：</strong>直接从原始数据到最终结果<br><br>
            
            <strong>关键技术突破：</strong><br>
            • <strong>ReLU激活函数：</strong>解决梯度消失问题<br>
            • <strong>Dropout：</strong>防止过拟合<br>
            • <strong>批量归一化：</strong>加速训练过程<br>
            • <strong>残差连接：</strong>训练更深的网络<br><br>
            
            <strong>应用领域：</strong><br>
            • 计算机视觉（图像识别、目标检测）<br>
            • 自然语言处理（机器翻译、文本生成）<br>
            • 语音识别和合成<br>
            • 游戏AI（AlphaGo、游戏智能体）<br><br>
            
            <strong>深度学习 vs 传统机器学习：</strong><br>
            传统方法需要专家设计特征，深度学习能够自动发现最有用的特征表示。
        `,
        visualization: "deep"
    },
    llm: {
        title: "🤖 通向大语言模型",
        description: `
            <strong>从神经网络到LLM的演进</strong><br>
            大语言模型(LLM)是深度学习在自然语言处理领域的巅峰应用，它们基于Transformer架构，具有数十亿甚至数千亿参数。<br><br>
            
            <strong>关键发展历程：</strong><br>
            • <strong>RNN时代：</strong>循环神经网络处理序列数据<br>
            • <strong>LSTM/GRU：</strong>解决长序列记忆问题<br>
            • <strong>注意力机制：</strong>让模型关注重要信息<br>
            • <strong>Transformer：</strong>完全基于注意力的架构<br>
            • <strong>预训练模型：</strong>BERT、GPT等大规模预训练<br><br>
            
            <strong>LLM的神经网络基础：</strong><br>
            • <strong>规模：</strong>数十亿到数千亿参数的深度网络<br>
            • <strong>架构：</strong>基于Transformer的多层注意力网络<br>
            • <strong>训练：</strong>在海量文本数据上进行预训练<br>
            • <strong>涌现能力：</strong>规模达到临界点后出现的新能力<br><br>
            
            <strong>从基础到应用：</strong><br>
            1. 神经元 → 感知器 → 多层网络<br>
            2. 深度学习 → 注意力机制 → Transformer<br>
            3. 预训练 → 微调 → 大语言模型<br>
            4. ChatGPT、GPT-4等实际应用<br><br>
            
            <strong>未来展望：</strong><br>
            LLM正在向多模态、更大规模、更高效的方向发展，将继续推动AI技术的边界。
        `,
        visualization: "llm"
    }
};

function openNeuralDemo() {
    const modal = document.getElementById('neuralModal');
    const content = document.getElementById('neuralModalContent');

    content.innerHTML = createNeuralDemoContent();
    modal.style.display = 'block';

    // 防止背景滚动
    document.body.style.overflow = 'hidden';

    // 初始化神经网络演示
    initializeNeuralDemo();
}

function closeNeuralDemo() {
    // 停止任何正在运行的动画
    pauseAnimation();

    // 恢复背景滚动
    document.body.style.overflow = 'auto';

    document.getElementById('neuralModal').style.display = 'none';
}

function createNeuralDemoContent() {
    return `
        <div class="neural-demo-area">
            <div class="neural-header">
                <h2>🧠 神经网络与深度学习交互式演示</h2>
                <p>从单个神经元到深度网络的完整学习之旅</p>
            </div>
            
            <div class="neural-content">
                <div class="neural-sidebar">
                    <h3 style="color: #FFD700; margin-bottom: 20px;">📚 学习模块</h3>
                    
                    <div class="neural-step active" data-step="neuron">
                        <h4>1️⃣ 神经元基础</h4>
                        <p>理解人工神经元的工作原理，包括输入、权重、激活函数等核心概念</p>
                    </div>
                    
                    <div class="neural-step" data-step="perceptron">
                        <h4>2️⃣ 感知器模型</h4>
                        <p>学习最简单的神经网络模型，理解线性分类的基本原理</p>
                    </div>
                    
                    <div class="neural-step" data-step="multilayer">
                        <h4>3️⃣ 多层网络</h4>
                        <p>探索多层神经网络如何解决复杂的非线性问题</p>
                    </div>
                    
                    <div class="neural-step" data-step="backprop">
                        <h4>4️⃣ 反向传播</h4>
                        <p>理解神经网络如何通过反向传播算法进行学习</p>
                    </div>
                    
                    <div class="neural-step" data-step="deep">
                        <h4>5️⃣ 深度学习</h4>
                        <p>了解深度神经网络的特点和优势</p>
                    </div>
                    
                    <div class="neural-step" data-step="llm">
                        <h4>6️⃣ LLM连接</h4>
                        <p>理解神经网络如何发展为现代的大语言模型</p>
                    </div>
                </div>
                
                <div class="neural-main">
                    <div id="neuralVisualization" class="neural-visualization">
                        <!-- 可视化内容将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="control-panel" id="controlPanel">
                        <button class="control-btn active" id="startBtn" onclick="startAnimation()">▶️ 开始演示</button>
                        <button class="control-btn" id="pauseBtn" onclick="pauseAnimation()">⏸️ 暂停</button>
                        <button class="control-btn" id="resetBtn" onclick="resetAnimation()">🔄 重置</button>
                        <button class="control-btn" onclick="nextStep()">⏭️ 下一步</button>
                        <button class="neural-code-btn" id="neuralCodeBtn" onclick="showNeuralCoreCode()">💻 实现该效果的核心代码</button>
                    </div>
                    
                    <div class="info-panel">
                        <h4 id="stepTitle">神经元基础</h4>
                        <p id="stepDescription">神经元是神经网络的基本单元...</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function initializeNeuralDemo() {
    // 初始化步骤点击事件
    document.querySelectorAll('.neural-step').forEach(step => {
        step.addEventListener('click', function () {
            const stepType = this.dataset.step;
            switchNeuralStep(stepType);
        });
    });

    // 显示初始步骤
    switchNeuralStep('neuron');
}

function switchNeuralStep(stepType) {
    // 停止当前动画
    pauseAnimation();

    currentNeuralStep = stepType;

    // 更新侧边栏激活状态
    document.querySelectorAll('.neural-step').forEach(step => {
        step.classList.remove('active');
    });
    const targetStep = document.querySelector(`[data-step="${stepType}"]`);
    if (targetStep) {
        targetStep.classList.add('active');
    }

    // 更新信息面板
    const stepData = neuralSteps[stepType];
    const titleElement = document.getElementById('stepTitle');
    const descElement = document.getElementById('stepDescription');

    if (titleElement && stepData) {
        titleElement.textContent = stepData.title;
    }
    if (descElement && stepData) {
        descElement.innerHTML = stepData.description;
    }

    // 更新可视化
    updateNeuralVisualization(stepType);

    // 更新控制按钮显示
    updateControlButtonsVisibility(stepType);

    // 重置按钮状态
    setTimeout(() => {
        updateControlButtons();
    }, 100);
}

function updateNeuralVisualization(stepType) {
    const container = document.getElementById('neuralVisualization');

    switch (stepType) {
        case 'neuron':
            container.innerHTML = createNeuronVisualization();
            break;
        case 'perceptron':
            container.innerHTML = createPerceptronVisualization();
            break;
        case 'multilayer':
            container.innerHTML = createMultilayerVisualization();
            break;
        case 'backprop':
            container.innerHTML = createBackpropVisualization();
            break;
        case 'deep':
            container.innerHTML = createDeepVisualization();
            break;
        case 'llm':
            container.innerHTML = createLLMVisualization();
            break;
    }
}

// 动画控制函数
function startAnimation() {
    if (animationRunning) return;

    // 先停止之前的动画
    pauseAnimation();

    animationRunning = true;

    // 更新按钮状态
    updateControlButtons();

    // 根据当前步骤启动相应演示
    switch (currentNeuralStep) {
        case 'neuron':
            demonstrateNeuronCalculation();
            break;
        case 'perceptron':
            demonstratePerceptronLearning();
            break;
        case 'multilayer':
            demonstrateFeatureHierarchy();
            break;
        case 'backprop':
            demonstrateBackpropagation();
            break;
        case 'deep':
            demonstrateDeepLearning();
            break;
        case 'llm':
            demonstrateLLMEvolution();
            break;
        default:
            // 对于没有特定动画的步骤，显示提示
            showAnimationMessage();
            break;
    }
}

function pauseAnimation() {
    animationRunning = false;
    if (animationInterval) {
        clearInterval(animationInterval);
        animationInterval = null;
    }
    updateControlButtons();
}

function resetAnimation() {
    pauseAnimation();
    updateNeuralVisualization(currentNeuralStep);
}

function updateControlButtons() {
    const startBtn = document.querySelector('button[onclick="startAnimation()"]');
    const pauseBtn = document.querySelector('button[onclick="pauseAnimation()"]');

    if (startBtn && pauseBtn) {
        if (animationRunning) {
            startBtn.classList.remove('active');
            pauseBtn.classList.add('active');
        } else {
            startBtn.classList.add('active');
            pauseBtn.classList.remove('active');
        }
    }
}

function updateControlButtonsVisibility(stepType) {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');
    const neuralCodeBtn = document.getElementById('neuralCodeBtn');

    // 对于"深度学习"和"LLM连接"步骤，隐藏演示控制按钮和核心代码按钮
    if (stepType === 'deep' || stepType === 'llm') {
        if (startBtn) startBtn.style.display = 'none';
        if (pauseBtn) pauseBtn.style.display = 'none';
        if (resetBtn) resetBtn.style.display = 'none';
        if (neuralCodeBtn) neuralCodeBtn.style.display = 'none';
    } else {
        // 对于其他步骤，显示演示控制按钮和核心代码按钮
        if (startBtn) startBtn.style.display = 'inline-block';
        if (pauseBtn) pauseBtn.style.display = 'inline-block';
        if (resetBtn) resetBtn.style.display = 'inline-block';
        if (neuralCodeBtn) neuralCodeBtn.style.display = 'inline-block';
    }
}

function showAnimationMessage() {
    // 为没有特定动画的步骤显示信息
    const neurons = document.querySelectorAll('.neuron');
    let index = 0;

    animationInterval = setInterval(() => {
        // 清除之前的激活状态
        neurons.forEach(n => n.classList.remove('active'));

        // 激活当前神经元
        if (neurons[index]) {
            neurons[index].classList.add('active');
        }

        index = (index + 1) % neurons.length;
    }, 800);
}

function nextStep() {
    const steps = ['neuron', 'perceptron', 'multilayer', 'backprop', 'deep', 'llm'];
    const currentIndex = steps.indexOf(currentNeuralStep);
    const nextIndex = (currentIndex + 1) % steps.length;
    switchNeuralStep(steps[nextIndex]);
}

// 可视化创建函数
function createNeuronVisualization() {
    return `
        <div class="neuron-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🧠 神经元结构与计算过程</h4>

                <div class="neuron-structure">
                    <div class="input-section">
                        <h5>输入信号</h5>
                        <div class="input-item">
                            <span class="input-label">x₁ = 0.8</span>
                            <div class="weight-line" data-weight="0.5">
                                <span class="weight-label">w₁ = 0.5</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₂ = 0.3</span>
                            <div class="weight-line" data-weight="0.3">
                                <span class="weight-label">w₂ = 0.3</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₃ = 0.6</span>
                            <div class="weight-line" data-weight="0.7">
                                <span class="weight-label">w₃ = 0.7</span>
                            </div>
                        </div>
                        <div class="bias-item">
                            <span class="bias-label">偏置 b = 0.1</span>
                        </div>
                    </div>

                    <div class="neuron-core">
                        <div class="neuron-body" id="neuronCore">
                            <div class="sum-symbol">Σ</div>
                            <div class="activation-func">σ</div>
                        </div>
                        <div class="computation-steps" id="computationSteps">
                            <div class="step">1. 加权求和</div>
                            <div class="step">2. 加偏置</div>
                            <div class="step">3. 激活函数</div>
                        </div>
                    </div>

                    <div class="output-section">
                        <h5>输出结果</h5>
                        <div class="output-value" id="outputValue">0.73</div>
                        <div class="output-explanation">
                            激活后的输出值
                        </div>
                    </div>
                </div>

                <div class="calculation-detail">
                    <h5 style="color: #FFD700;">📊 详细计算过程</h5>
                    <div class="calc-step">
                        <strong>步骤1：</strong> 加权求和 = x₁×w₁ + x₂×w₂ + x₃×w₃ = 0.8×0.5 + 0.3×0.3 + 0.6×0.7 = 0.89
                    </div>
                    <div class="calc-step">
                        <strong>步骤2：</strong> 加偏置 = 0.89 + 0.1 = 0.99
                    </div>
                    <div class="calc-step">
                        <strong>步骤3：</strong> 激活函数 σ(0.99) = 1/(1+e^(-0.99)) ≈ 0.73
                    </div>
                </div>

                <div class="activation-functions">
                    <h5 style="color: #FFD700;">⚡ 常见激活函数</h5>
                    <div class="func-grid">
                        <div class="func-item">
                            <strong>Sigmoid:</strong> σ(x) = 1/(1+e^(-x))<br>
                            <small>输出范围: (0,1)</small>
                        </div>
                        <div class="func-item">
                            <strong>ReLU:</strong> f(x) = max(0,x)<br>
                            <small>输出范围: [0,+∞)</small>
                        </div>
                        <div class="func-item">
                            <strong>Tanh:</strong> f(x) = (e^x-e^(-x))/(e^x+e^(-x))<br>
                            <small>输出范围: (-1,1)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createPerceptronVisualization() {
    return `
        <div class="perceptron-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">⚡ 感知器：二分类学习器</h4>

                <div class="perceptron-example">
                    <h5>实例：判断是否为运动员</h5>
                    <div class="training-data">
                        <div class="data-table">
                            <div class="table-header">
                                <span>身高(cm)</span>
                                <span>体重(kg)</span>
                                <span>年龄</span>
                                <span>标签</span>
                            </div>
                            <div class="table-row positive">
                                <span>185</span><span>80</span><span>25</span><span class="label athlete">运动员</span>
                            </div>
                            <div class="table-row positive">
                                <span>178</span><span>75</span><span>28</span><span class="label athlete">运动员</span>
                            </div>
                            <div class="table-row negative">
                                <span>165</span><span>60</span><span>35</span><span class="label normal">普通人</span>
                            </div>
                            <div class="table-row negative">
                                <span>170</span><span>65</span><span>40</span><span class="label normal">普通人</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="perceptron-structure">
                    <h5 style="color: #FFD700;">🔄 感知器学习过程</h5>
                    <div class="learning-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>初始化权重</strong><br>
                                随机设置权重：w₁=0.2, w₂=0.3, w₃=0.1, b=0.5
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>前向计算</strong><br>
                                输入样本 → 计算加权和 → 激活函数 → 预测结果
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>误差计算</strong><br>
                                比较预测值与真实标签，计算误差
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <strong>权重更新</strong><br>
                                根据误差调整权重：w = w + η × (真实值 - 预测值) × 输入
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <strong>重复训练</strong><br>
                                重复步骤2-4，直到模型收敛或达到最大迭代次数
                            </div>
                        </div>
                    </div>
                </div>

                <div class="decision-boundary">
                    <h5 style="color: #FFD700;">📊 决策边界可视化</h5>
                    <div class="boundary-explanation">
                        <p>感知器学习的本质是找到一个超平面（在2D中是直线），将不同类别的数据分开。</p>
                        <div class="boundary-formula">
                            <strong>决策边界方程：</strong> w₁×身高 + w₂×体重 + w₃×年龄 + b = 0
                        </div>
                        <div class="classification-rule">
                            <div class="rule-item positive-rule">
                                <strong>运动员：</strong> 当 w₁×x₁ + w₂×x₂ + w₃×x₃ + b > 0
                            </div>
                            <div class="rule-item negative-rule">
                                <strong>普通人：</strong> 当 w₁×x₁ + w₂×x₂ + w₃×x₃ + b ≤ 0
                            </div>
                        </div>
                    </div>
                </div>

                <div class="limitations">
                    <h5 style="color: #FFD700;">⚠️ 感知器的局限性</h5>
                    <div class="limitation-grid">
                        <div class="limitation-item">
                            <strong>线性可分性：</strong><br>
                            只能解决线性可分的问题，无法处理XOR等非线性问题
                        </div>
                        <div class="limitation-item">
                            <strong>单层结构：</strong><br>
                            只有一层权重，表达能力有限
                        </div>
                        <div class="limitation-item">
                            <strong>二分类限制：</strong><br>
                            原始感知器只能进行二分类任务
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createMultilayerVisualization() {
    return `
        <div class="multilayer-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🏗️ 多层神经网络：突破线性限制</h4>

                <div class="network-evolution">
                    <h5>从单层到多层的演进</h5>
                    <div class="evolution-comparison">
                        <div class="evolution-item">
                            <h6>单层感知器</h6>
                            <div class="simple-network">
                                <div class="layer-simple">输入</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">输出</div>
                            </div>
                            <div class="capability">只能解决线性可分问题</div>
                        </div>
                        <div class="evolution-item">
                            <h6>多层神经网络</h6>
                            <div class="complex-network">
                                <div class="layer-simple">输入</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">隐藏层</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">输出</div>
                            </div>
                            <div class="capability">可以解决非线性问题</div>
                        </div>
                    </div>
                </div>

                <div class="xor-problem">
                    <h5 style="color: #FFD700;">🔍 经典案例：XOR问题</h5>
                    <div class="xor-explanation">
                        <div class="xor-table">
                            <h6>XOR真值表</h6>
                            <table>
                                <tr><th>输入A</th><th>输入B</th><th>输出</th></tr>
                                <tr><td>0</td><td>0</td><td>0</td></tr>
                                <tr><td>0</td><td>1</td><td>1</td></tr>
                                <tr><td>1</td><td>0</td><td>1</td></tr>
                                <tr><td>1</td><td>1</td><td>0</td></tr>
                            </table>
                        </div>
                        <div class="xor-solution">
                            <h6>多层网络解决方案</h6>
                            <div class="xor-network">
                                <div class="xor-layer">
                                    <div class="xor-title">输入层</div>
                                    <div class="xor-node">A</div>
                                    <div class="xor-node">B</div>
                                </div>
                                <div class="xor-layer">
                                    <div class="xor-title">隐藏层</div>
                                    <div class="xor-node">AND门</div>
                                    <div class="xor-node">OR门</div>
                                </div>
                                <div class="xor-layer">
                                    <div class="xor-title">输出层</div>
                                    <div class="xor-node">XOR</div>
                                </div>
                            </div>
                            <div class="xor-formula">
                                XOR = OR AND (NOT AND)
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-hierarchy">
                    <h5 style="color: #FFD700;">📊 特征层次化学习</h5>
                    <div class="hierarchy-example">
                        <div class="hierarchy-title">图像识别中的特征提取</div>
                        <div class="hierarchy-layers">
                            <div class="hierarchy-layer">
                                <div class="layer-name">输入层</div>
                                <div class="layer-features">原始像素</div>
                                <div class="feature-examples">
                                    <div class="pixel-grid">
                                        <div class="pixel"></div><div class="pixel"></div><div class="pixel"></div>
                                        <div class="pixel"></div><div class="pixel active"></div><div class="pixel"></div>
                                        <div class="pixel"></div><div class="pixel"></div><div class="pixel"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">隐藏层1</div>
                                <div class="layer-features">边缘检测</div>
                                <div class="feature-examples">
                                    <div class="edge-patterns">
                                        <div class="edge horizontal"></div>
                                        <div class="edge vertical"></div>
                                        <div class="edge diagonal"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">隐藏层2</div>
                                <div class="layer-features">形状组合</div>
                                <div class="feature-examples">
                                    <div class="shape-patterns">
                                        <div class="shape circle"></div>
                                        <div class="shape triangle"></div>
                                        <div class="shape square"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">输出层</div>
                                <div class="layer-features">对象识别</div>
                                <div class="feature-examples">
                                    <div class="object-labels">
                                        <div class="label">猫</div>
                                        <div class="label">狗</div>
                                        <div class="label">鸟</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="universal-approximation">
                    <h5 style="color: #FFD700;">🎯 万能逼近定理</h5>
                    <div class="theorem-explanation">
                        <div class="theorem-statement">
                            <strong>定理：</strong>具有足够多隐藏单元的单隐藏层前馈网络可以逼近任何连续函数到任意精度。
                        </div>
                        <div class="theorem-implications">
                            <div class="implication">
                                <strong>理论意义：</strong>多层网络具有强大的表达能力
                            </div>
                            <div class="implication">
                                <strong>实践意义：</strong>深度网络通常比宽度网络更高效
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createBackpropVisualization() {
    return `
        <div class="backprop-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🔄 反向传播：神经网络的学习算法</h4>

                <div class="backprop-overview">
                    <h5>算法核心思想</h5>
                    <div class="algorithm-steps">
                        <div class="algo-step forward">
                            <div class="step-icon">→</div>
                            <div class="step-content">
                                <strong>前向传播</strong><br>
                                输入数据通过网络，计算预测输出
                            </div>
                        </div>
                        <div class="algo-step loss">
                            <div class="step-icon">📊</div>
                            <div class="step-content">
                                <strong>损失计算</strong><br>
                                比较预测值与真实值，计算误差
                            </div>
                        </div>
                        <div class="algo-step backward">
                            <div class="step-icon">←</div>
                            <div class="step-content">
                                <strong>反向传播</strong><br>
                                误差从输出层向输入层传播
                            </div>
                        </div>
                        <div class="algo-step update">
                            <div class="step-icon">🔧</div>
                            <div class="step-content">
                                <strong>权重更新</strong><br>
                                根据梯度调整网络参数
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gradient-descent">
                    <h5 style="color: #FFD700;">📈 梯度下降可视化</h5>
                    <div class="gradient-explanation">
                        <div class="mountain-analogy">
                            <h6>🏔️ 山坡类比</h6>
                            <div class="mountain-visual">
                                <div class="mountain-curve">
                                    <div class="ball" id="gradientBall">⚫</div>
                                    <div class="valley">最优解</div>
                                </div>
                            </div>
                            <p>想象在山坡上找最低点：</p>
                            <ul>
                                <li><strong>当前位置：</strong>网络的当前参数状态</li>
                                <li><strong>坡度：</strong>损失函数的梯度</li>
                                <li><strong>下山方向：</strong>梯度的反方向</li>
                                <li><strong>步长：</strong>学习率（每次移动的距离）</li>
                            </ul>
                        </div>

                        <div class="gradient-formula">
                            <h6>📐 数学公式</h6>
                            <div class="formula-box">
                                <div class="formula-item">
                                    <strong>权重更新规则：</strong><br>
                                    w_new = w_old - η × ∂L/∂w
                                </div>
                                <div class="formula-item">
                                    <strong>链式法则：</strong><br>
                                    ∂L/∂w = ∂L/∂y × ∂y/∂z × ∂z/∂w
                                </div>
                            </div>
                            <div class="formula-explanation">
                                <p><strong>η (eta)：</strong>学习率，控制更新步长</p>
                                <p><strong>∂L/∂w：</strong>损失函数对权重的偏导数（梯度）</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="backprop-example">
                    <h5 style="color: #FFD700;">🔢 具体计算示例</h5>
                    <div class="calculation-flow">
                        <div class="calc-phase">
                            <h6>前向传播</h6>
                            <div class="calc-steps">
                                <div class="calc-item">输入: x = 2</div>
                                <div class="calc-item">权重: w = 0.5</div>
                                <div class="calc-item">预测: ŷ = x × w = 1.0</div>
                                <div class="calc-item">真实值: y = 3</div>
                                <div class="calc-item">损失: L = ½(y - ŷ)² = 2.0</div>
                            </div>
                        </div>

                        <div class="calc-phase">
                            <h6>反向传播</h6>
                            <div class="calc-steps">
                                <div class="calc-item">∂L/∂ŷ = -(y - ŷ) = -2</div>
                                <div class="calc-item">∂ŷ/∂w = x = 2</div>
                                <div class="calc-item">∂L/∂w = ∂L/∂ŷ × ∂ŷ/∂w = -4</div>
                                <div class="calc-item">w_new = w - η × ∂L/∂w = 0.5 - 0.1 × (-4) = 0.9</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="backprop-advantages">
                    <h5 style="color: #FFD700;">✨ 反向传播的重要性</h5>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <strong>高效计算：</strong><br>
                            一次反向传播可以计算所有参数的梯度
                        </div>
                        <div class="advantage-item">
                            <strong>自动微分：</strong><br>
                            无需手动计算复杂的导数
                        </div>
                        <div class="advantage-item">
                            <strong>可扩展性：</strong><br>
                            适用于任意深度和复杂度的网络
                        </div>
                        <div class="advantage-item">
                            <strong>现代AI基础：</strong><br>
                            几乎所有深度学习框架的核心算法
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createDeepVisualization() {
    return `
        <div class="network-container" style="justify-content: space-between;">
            <div class="layer">
                <div class="layer-title">输入</div>
                <div class="neuron">像素1</div>
                <div class="neuron">像素2</div>
                <div class="neuron">像素3</div>
                <div class="neuron">...</div>
                <div class="neuron">像素n</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层1</div>
                <div class="neuron">边缘</div>
                <div class="neuron">纹理</div>
                <div class="neuron">形状</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层2</div>
                <div class="neuron">眼睛</div>
                <div class="neuron">鼻子</div>
                <div class="neuron">嘴巴</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层3</div>
                <div class="neuron">面部</div>
                <div class="neuron">表情</div>
            </div>

            <div class="layer">
                <div class="layer-title">分类</div>
                <div class="neuron">人脸</div>
                <div class="neuron">非人脸</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; color: white;">
            <p>深度学习：从像素到高级概念的层次化特征学习</p>
            <p style="font-size: 0.9em; opacity: 0.8;">每一层都在前一层的基础上学习更抽象的特征</p>
        </div>
    `;
}

function createLLMVisualization() {
    return `
        <div class="network-container" style="flex-direction: column; gap: 30px;">
            <div style="display: flex; justify-content: space-around; width: 100%;">
                <div class="layer">
                    <div class="layer-title">传统神经网络</div>
                    <div style="display: flex; gap: 10px;">
                        <div class="neuron small">输入</div>
                        <div class="neuron small">隐藏</div>
                        <div class="neuron small">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数百个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">深度网络</div>
                    <div style="display: flex; gap: 5px;">
                        <div class="neuron small">输入</div>
                        <div class="neuron small">层1</div>
                        <div class="neuron small">层2</div>
                        <div class="neuron small">层3</div>
                        <div class="neuron small">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数万个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">Transformer</div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 3px;">
                        <div class="neuron tiny">注意力</div>
                        <div class="neuron tiny">前馈</div>
                        <div class="neuron tiny">归一化</div>
                        <div class="neuron tiny">残差</div>
                        <div class="neuron tiny">多头</div>
                        <div class="neuron tiny">编码</div>
                        <div class="neuron tiny">解码</div>
                        <div class="neuron tiny">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数亿个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">大语言模型</div>
                    <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 2px;">
                        ${Array(24).fill().map(() => '<div class="neuron tiny">T</div>').join('')}
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数千亿参数</p>
                </div>
            </div>

            <div style="text-align: center; color: white;">
                <h4 style="color: #FFD700; margin-bottom: 15px;">从神经网络到LLM的演进</h4>
                <p>参数规模的指数级增长 → 涌现能力的出现 → 通用人工智能的曙光</p>
            </div>
        </div>
    `;
}

// 新的交互式演示函数 - 替代简单的动画
function demonstrateNeuronCalculation() {
    // 为神经元演示添加交互式计算
    const steps = document.querySelectorAll('.calc-step');
    let currentStep = 0;

    const highlightStep = () => {
        steps.forEach(step => step.style.background = 'rgba(255, 255, 255, 0.05)');
        if (steps[currentStep]) {
            steps[currentStep].style.background = 'rgba(255, 215, 0, 0.3)';
            steps[currentStep].style.border = '2px solid #FFD700';
        }
        currentStep = (currentStep + 1) % steps.length;
    };

    // 每2秒高亮下一步
    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightStep();
    }, 2000);
}

function demonstratePerceptronLearning() {
    // 高亮学习步骤
    const stepItems = document.querySelectorAll('.step-item');
    let currentStep = 0;

    const highlightLearningStep = () => {
        stepItems.forEach(item => {
            item.style.background = 'rgba(255, 255, 255, 0.05)';
            item.style.border = 'none';
        });

        if (stepItems[currentStep]) {
            stepItems[currentStep].style.background = 'rgba(76, 175, 80, 0.3)';
            stepItems[currentStep].style.border = '2px solid #4CAF50';
        }
        currentStep = (currentStep + 1) % stepItems.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightLearningStep();
    }, 1800);
}

function demonstrateFeatureHierarchy() {
    // 演示特征层次化学习
    const hierarchyLayers = document.querySelectorAll('.hierarchy-layer');
    let currentLayer = 0;

    const highlightLayer = () => {
        hierarchyLayers.forEach(layer => {
            layer.style.background = 'transparent';
            layer.style.border = 'none';
        });

        if (hierarchyLayers[currentLayer]) {
            hierarchyLayers[currentLayer].style.background = 'rgba(78, 205, 196, 0.2)';
            hierarchyLayers[currentLayer].style.border = '2px solid #4ECDC4';
            hierarchyLayers[currentLayer].style.borderRadius = '10px';
            hierarchyLayers[currentLayer].style.padding = '15px';
        }
        currentLayer = (currentLayer + 1) % hierarchyLayers.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightLayer();
    }, 2500);
}

function demonstrateBackpropagation() {
    // 演示反向传播的计算流程
    const calcPhases = document.querySelectorAll('.calc-phase');
    let currentPhase = 0;

    const highlightPhase = () => {
        calcPhases.forEach(phase => {
            phase.style.background = 'rgba(255, 255, 255, 0.05)';
            phase.style.border = 'none';
        });

        if (calcPhases[currentPhase]) {
            calcPhases[currentPhase].style.background = 'rgba(255, 107, 107, 0.3)';
            calcPhases[currentPhase].style.border = '2px solid #FF6B6B';
            calcPhases[currentPhase].style.borderRadius = '10px';
            calcPhases[currentPhase].style.padding = '15px';
        }
        currentPhase = (currentPhase + 1) % calcPhases.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightPhase();
    }, 3000);
}

function demonstrateDeepLearning() {
    // 深度学习演示：简单的神经元闪烁效果
    const neurons = document.querySelectorAll('.neuron');
    let index = 0;

    const highlightNeuron = () => {
        neurons.forEach(n => n.classList.remove('active'));

        if (neurons[index]) {
            neurons[index].classList.add('active');
        }
        index = (index + 1) % neurons.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightNeuron();
    }, 800);
}

function demonstrateLLMEvolution() {
    // LLM演示：按层级高亮不同的网络架构
    const layers = document.querySelectorAll('.layer');
    let currentLayer = 0;

    const highlightLayer = () => {
        layers.forEach(layer => {
            layer.style.background = 'transparent';
            layer.style.border = 'none';
        });

        if (layers[currentLayer]) {
            layers[currentLayer].style.background = 'rgba(255, 215, 0, 0.2)';
            layers[currentLayer].style.border = '2px solid #FFD700';
            layers[currentLayer].style.borderRadius = '10px';
            layers[currentLayer].style.padding = '15px';
        }
        currentLayer = (currentLayer + 1) % layers.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightLayer();
    }, 1500);
}

// ==================== 神经网络核心代码功能 ====================

function showNeuralCoreCode() {
    console.log('showNeuralCoreCode called, currentNeuralStep:', currentNeuralStep);

    // 检查NeuralNetworkCoreCode是否已加载
    if (typeof NeuralNetworkCoreCode === 'undefined') {
        console.error('NeuralNetworkCoreCode is undefined');
        alert('神经网络核心代码库正在加载中，请稍后再试...');
        return;
    }

    console.log('NeuralNetworkCoreCode available:', Object.keys(NeuralNetworkCoreCode));

    const codeData = NeuralNetworkCoreCode[currentNeuralStep];

    if (!codeData) {
        console.error('No code data found for step:', currentNeuralStep);
        alert('未找到该模块的核心代码');
        return;
    }

    console.log('Code data found:', codeData.title);

    // 直接使用简单弹窗，确保能够显示
    showSimpleCodeModal(codeData);
}

function showSimpleCodeModal(codeData) {
    // 创建简单的代码显示弹窗
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 3000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        border-radius: 15px;
        width: 90%;
        max-width: 1000px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        color: white;
    `;

    content.innerHTML = `
        <div style="padding: 20px; border-bottom: 2px solid rgba(255, 255, 255, 0.1); display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 1.4em;">${codeData.title}</h3>
            <span style="font-size: 28px; cursor: pointer; background: rgba(255, 255, 255, 0.1); border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;" onclick="this.closest('.code-modal-temp').remove()">&times;</span>
        </div>
        <div style="padding: 25px; max-height: calc(80vh - 100px); overflow-y: auto;">
            <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; border-left: 4px solid #6c5ce7;">
                ${codeData.description}
            </div>
            <div style="background: #1a1a1a; border-radius: 12px; overflow: hidden;">
                <div style="background: linear-gradient(45deg, #2d3748, #4a5568); padding: 12px 20px; display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: bold; color: #ffd700;">JavaScript</span>
                    <button onclick="copyNeuralCode(this)" style="background: linear-gradient(45deg, #48bb78, #38a169); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 0.8em; cursor: pointer;">📋 复制代码</button>
                </div>
                <pre style="background: #1a1a1a; color: #e2e8f0; padding: 20px; margin: 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; line-height: 1.6; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;">${codeData.code}</pre>
            </div>
        </div>
    `;

    modal.className = 'code-modal-temp';
    modal.appendChild(content);
    document.body.appendChild(modal);

    // 点击外部关闭
    modal.addEventListener('click', function (e) {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // ESC键关闭
    const escHandler = function (e) {
        if (e.key === 'Escape') {
            modal.remove();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

function copyNeuralCode(button) {
    const codeElement = button.closest('.code-modal-temp').querySelector('pre');
    const textArea = document.createElement('textarea');
    textArea.value = codeElement.textContent;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        const originalText = button.textContent;
        button.textContent = '✅ 已复制!';
        button.style.background = 'linear-gradient(45deg, #48bb78, #38a169)';

        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = 'linear-gradient(45deg, #48bb78, #38a169)';
        }, 2000);
    } catch (err) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(codeElement.textContent).then(() => {
                const originalText = button.textContent;
                button.textContent = '✅ 已复制!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(() => {
                alert('复制失败，请手动选择代码进行复制');
            });
        } else {
            alert('复制失败，请手动选择代码进行复制');
        }
    }

    document.body.removeChild(textArea);
}

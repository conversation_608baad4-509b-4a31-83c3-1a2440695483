// 简化的全局变量 - 现在只需要交互功能

// 动画步骤定义
const transformerSteps = [
    {
        title: "步骤1: 输入序列处理",
        description: "将输入文本转换为词嵌入向量，为后续处理做准备",
        targets: ['inputs', 'input-embedding'],
        duration: 2000
    },
    {
        title: "步骤2: 编码器位置编码",
        description: "为输入序列添加位置信息，让模型理解词语的顺序关系",
        targets: ['encoder-pos'],
        duration: 2000
    },
    {
        title: "步骤3: 编码器多头自注意力",
        description: "让序列中每个词都能关注到其他所有词，捕获全局依赖关系",
        targets: ['encoder-attention'],
        duration: 3000
    },
    {
        title: "步骤4: 编码器残差连接与归一化",
        description: "残差连接保留原始信息，层归一化稳定训练过程",
        targets: ['encoder-norm1'],
        duration: 2000
    },
    {
        title: "步骤5: 编码器前馈网络",
        description: "两层全连接网络，对每个位置独立进行非线性变换",
        targets: ['encoder-ffn'],
        duration: 2500
    },
    {
        title: "步骤6: 编码器第二次归一化",
        description: "完成编码器层的处理，准备传递给解码器",
        targets: ['encoder-norm2'],
        duration: 2000
    },
    {
        title: "步骤7: 目标序列输入",
        description: "处理目标序列（训练时）或已生成的部分（推理时）",
        targets: ['target-inputs', 'target-embedding'],
        duration: 2000
    },
    {
        title: "步骤8: 解码器位置编码",
        description: "为目标序列添加位置信息",
        targets: ['decoder-pos'],
        duration: 2000
    },
    {
        title: "步骤9: 掩码多头自注意力",
        description: "防止解码器看到未来的词，保持自回归生成特性",
        targets: ['decoder-masked-attention'],
        duration: 3000
    },
    {
        title: "步骤10: 解码器第一次归一化",
        description: "对掩码自注意力的输出进行处理",
        targets: ['decoder-norm1'],
        duration: 2000
    },
    {
        title: "步骤11: 编码器-解码器注意力",
        description: "解码器通过注意力机制关注编码器的输出，建立源序列和目标序列的联系",
        targets: ['decoder-cross-attention'],
        duration: 3000
    },
    {
        title: "步骤12: 解码器第二次归一化",
        description: "对交叉注意力的输出进行处理",
        targets: ['decoder-norm2'],
        duration: 2000
    },
    {
        title: "步骤13: 解码器前馈网络",
        description: "与编码器相同的前馈网络结构",
        targets: ['decoder-ffn'],
        duration: 2500
    },
    {
        title: "步骤14: 解码器最终归一化",
        description: "完成解码器层的最后处理步骤",
        targets: ['decoder-norm3'],
        duration: 2000
    },
    {
        title: "步骤15: 线性层变换",
        description: "将解码器输出映射到词汇表大小的向量",
        targets: ['linear-layer'],
        duration: 2000
    },
    {
        title: "步骤16: Softmax概率分布",
        description: "将线性层输出转换为词汇表上的概率分布",
        targets: ['softmax-layer'],
        duration: 2000
    },
    {
        title: "步骤17: 输出概率",
        description: "生成最终的词汇概率，完成一次前向传播",
        targets: ['outputs'],
        duration: 2500
    }
];

// 初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeEventListeners();
    updateSpeedDisplay();
    resetAnimation();
});

// 初始化事件监听器
function initializeEventListeners() {
    document.getElementById('start-animation').addEventListener('click', startAnimation);
    document.getElementById('pause-animation').addEventListener('click', pauseAnimation);
    document.getElementById('reset-animation').addEventListener('click', resetAnimation);

    const speedSlider = document.getElementById('speed-slider');
    speedSlider.addEventListener('input', function () {
        animationSpeed = parseFloat(this.value);
        updateSpeedDisplay();
    });

    // 移除切换视图功能，因为现在是统一的架构图
    const toggleBtn = document.getElementById('toggle-view');
    if (toggleBtn) {
        toggleBtn.style.display = 'none';
    }
}

// 开始动画
function startAnimation() {
    if (isPaused) {
        resumeAnimation();
        return;
    }

    if (isAnimating) return;

    isAnimating = true;
    isPaused = false;
    currentStep = 0;

    updateControlButtons();
    runAnimationSequence();
}

// 暂停动画
function pauseAnimation() {
    if (!isAnimating) return;

    isPaused = true;
    clearTimeout(animationInterval);
    updateControlButtons();
}

// 恢复动画
function resumeAnimation() {
    if (!isPaused) return;

    isPaused = false;
    updateControlButtons();
    runAnimationSequence();
}

// 重置动画
function resetAnimation() {
    isAnimating = false;
    isPaused = false;
    currentStep = 0;

    clearTimeout(animationInterval);
    clearAllHighlights();
    updateControlButtons();
    updateStepInfo("准备开始演示", "点击"开始演示"按钮来观看Transformer架构的工作流程");
}

// 移除切换视图功能，因为现在是统一的架构图

// 运行动画序列
function runAnimationSequence() {
    const steps = transformerSteps;

    if (currentStep >= steps.length) {
        completeAnimation();
        return;
    }

    const step = steps[currentStep];
    executeAnimationStep(step);

    const adjustedDuration = step.duration / animationSpeed;

    animationInterval = setTimeout(() => {
        if (!isPaused) {
            currentStep++;
            runAnimationSequence();
        }
    }, adjustedDuration);
}

// 执行单个动画步骤
function executeAnimationStep(step) {
    // 更新步骤信息
    updateStepInfo(step.title, step.description);

    // 清除之前的高亮
    clearAllHighlights();

    // 高亮当前步骤的目标元素
    step.targets.forEach(targetId => {
        const element = document.getElementById(targetId);
        if (element) {
            highlightElement(element);
        }
    });

    // 添加数据流动动画
    addDataFlowAnimation(step.targets);
}

// 高亮元素
function highlightElement(element) {
    element.classList.add('active');

    // 添加高亮效果
    element.style.transform = 'scale(1.05)';
    element.style.boxShadow = '0 8px 30px rgba(102, 126, 234, 0.4)';
    element.style.zIndex = '10';
    element.style.transition = 'all 0.3s ease';

    // 显示知识提示
    const tooltip = element.querySelector('.knowledge-tooltip');
    if (tooltip) {
        tooltip.style.display = 'block';
    }
}

// 清除所有高亮
function clearAllHighlights() {
    const highlightedElements = document.querySelectorAll('.active');
    highlightedElements.forEach(element => {
        element.classList.remove('active');
        element.style.transform = '';
        element.style.boxShadow = '';
        element.style.zIndex = '';

        // 隐藏知识提示
        const tooltip = element.querySelector('.knowledge-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    });
}

// 添加数据流动动画
function addDataFlowAnimation(targets) {
    targets.forEach(targetId => {
        const element = document.getElementById(targetId);
        if (element) {
            const dataFlow = element.querySelector('.data-flow');
            if (dataFlow) {
                dataFlow.classList.add('animating');

                // 移除动画类，以便下次可以重新添加
                setTimeout(() => {
                    dataFlow.classList.remove('animating');
                }, 2000);
            }
        }
    });
}

// 完成动画
function completeAnimation() {
    isAnimating = false;
    isPaused = false;

    updateControlButtons();
    updateStepInfo(
        "🎉 演示完成！",
        "Transformer完整架构演示已完成。您可以重新开始演示或点击任意组件查看详细信息。"
    );

    // 添加完成效果
    setTimeout(() => {
        clearAllHighlights();
    }, 3000);
}

// 更新控制按钮状态
function updateControlButtons() {
    const startBtn = document.getElementById('start-animation');
    const pauseBtn = document.getElementById('pause-animation');
    const resetBtn = document.getElementById('reset-animation');

    if (isAnimating && !isPaused) {
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        startBtn.textContent = '▶️ 运行中...';
        pauseBtn.textContent = '⏸️ 暂停';
    } else if (isPaused) {
        startBtn.disabled = false;
        pauseBtn.disabled = true;
        startBtn.textContent = '▶️ 继续';
        pauseBtn.textContent = '⏸️ 已暂停';
    } else {
        startBtn.disabled = false;
        pauseBtn.disabled = true;
        startBtn.textContent = '▶️ 开始演示';
        pauseBtn.textContent = '⏸️ 暂停';
    }

    resetBtn.disabled = false;
}

// 更新步骤信息
function updateStepInfo(title, description) {
    document.getElementById('step-title').textContent = title;
    document.getElementById('step-description').textContent = description;
}

// 更新速度显示
function updateSpeedDisplay() {
    document.getElementById('speed-value').textContent = animationSpeed + 'x';
}

// 添加鼠标悬停效果
document.addEventListener('DOMContentLoaded', function () {
    // 为所有组件添加悬停效果
    const components = document.querySelectorAll('.component');
    components.forEach(component => {
        component.addEventListener('mouseenter', function () {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.15)';
            }
        });

        component.addEventListener('mouseleave', function () {
            if (!this.classList.contains('active')) {
                this.style.transform = '';
                this.style.boxShadow = '';
            }
        });

        // 添加点击效果显示详细信息
        component.addEventListener('click', function () {
            showComponentDetails(this);
        });
    });
});

// 显示组件详细信息
function showComponentDetails(component) {
    const title = component.querySelector('h4')?.textContent || '组件详情';
    const description = component.querySelector('.knowledge-point')?.textContent || '暂无详细信息';

    // 创建模态框显示详细信息
    const modal = document.createElement('div');
    modal.className = 'detail-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>${description}</p>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加关闭事件
    modal.querySelector('.close-btn').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    // 添加模态框样式
    if (!document.querySelector('#modal-styles')) {
        const modalStyles = document.createElement('style');
        modalStyles.id = 'modal-styles';
        modalStyles.textContent = `
            .detail-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .modal-header h3 {
                margin: 0;
                color: #2d3748;
            }
            
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #718096;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
            }
            
            .close-btn:hover {
                background: #f7fafc;
                color: #2d3748;
            }
            
            .modal-body {
                padding: 20px;
                line-height: 1.6;
                color: #4a5568;
            }
        `;
        document.head.appendChild(modalStyles);
    }
}

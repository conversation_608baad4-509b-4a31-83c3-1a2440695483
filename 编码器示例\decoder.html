<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transformer解码器流程演示</title>
    <link rel="stylesheet" href="decoder-style.css">
</head>

<body>
    <div class="container">
        <header>
            <h1>Transformer解码器运行流程演示</h1>
            <p class="subtitle">深入理解解码器的每一个步骤</p>
        </header>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <button id="startDemo" class="btn-primary">开始演示</button>
                <button id="resetDemo" class="btn-secondary">重置</button>
                <button id="nextStep" class="btn-step" disabled>下一步</button>
                <button id="prevStep" class="btn-step" disabled>上一步</button>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="step-counter" id="stepCounter">步骤 0/7</span>
            </div>

            <!-- 三栏布局：架构图 + 步骤演示 + 知识点详解 -->
            <div class="three-column-layout">
                <!-- 左侧：整体架构图 -->
                <div class="architecture-overview">
                    <h3>解码器整体架构</h3>
                    <div class="decoder-block" id="decoderBlock">
                        <div class="component" id="target-embedding">
                            <span>目标输入嵌入 + 位置编码</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="masked-attention">
                            <span>掩码多头自注意力机制</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="addnorm1">
                            <span>残差连接 + 层归一化</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="cross-attention">
                            <span>编码器-解码器注意力机制</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="addnorm2">
                            <span>残差连接 + 层归一化</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="ffn">
                            <span>前馈神经网络</span>
                        </div>
                        <div class="arrow">↓</div>
                        <div class="component" id="addnorm3">
                            <span>残差连接 + 层归一化</span>
                        </div>
                    </div>
                </div>

                <!-- 中间：步骤演示 -->
                <div class="step-visualization-panel">
                    <h3 id="stepTitle">点击"开始演示"开始学习</h3>
                    <div class="step-description" id="stepDescription">
                        <p>欢迎来到Transformer解码器流程演示！</p>
                        <p>本演示将带您逐步了解解码器的每个组件及其工作原理。</p>
                    </div>
                    <div class="step-visualization" id="stepVisualization">
                        <!-- 动态可视化内容将在这里显示 -->
                    </div>
                </div>

                <!-- 右侧：知识点详解 -->
                <div class="knowledge-panel" id="knowledgePanel">
                    <h3>💡 知识点详解</h3>
                    <div class="knowledge-content" id="knowledgeContent">
                        <div class="welcome-knowledge">
                            <h4>🎯 学习目标</h4>
                            <ul>
                                <li>理解Transformer解码器的完整工作流程</li>
                                <li>掌握掩码注意力机制的作用原理</li>
                                <li>学会分析编码器-解码器注意力的工作方式</li>
                                <li>了解解码器如何生成目标序列</li>
                            </ul>
                            <h4>📚 使用说明</h4>
                            <p>点击"开始演示"按钮，系统将逐步引导您学习解码器的每个组件。解码器负责根据编码器的输出生成目标序列。</p>
                            <h4>🔗 相关链接</h4>
                            <p><a href="index.html" style="color: #4CAF50; text-decoration: none;">← 返回编码器演示</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="decoder-script.js"></script>
</body>

</html>

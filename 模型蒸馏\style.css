/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* 背景动画 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-particle {
    position: absolute;
    width: 100px;
    height: 100px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 15s infinite linear;
}

.floating-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-particle:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: -5s;
}

.floating-particle:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
}

.floating-particle:nth-child(4) {
    top: 40%;
    right: 40%;
    animation-delay: -7s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.1;
    }

    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.3;
    }
}

/* 主容器 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.main-title {
    font-size: 2.5em;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.title-icon {
    font-size: 1.2em;
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

.title-subtitle {
    font-size: 0.4em;
    color: #7f8c8d;
    font-weight: 400;
    display: block;
    margin-top: 5px;
}

.header-description {
    font-size: 1.1em;
    color: #5a6c7d;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* 导航标签 */
.demo-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 1em;
    font-weight: 600;
    color: #5a6c7d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.tab-icon {
    font-size: 1.2em;
}

/* 主要内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 30px;
}

.demo-section {
    display: none;
}

.demo-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* 演示容器 */
.demo-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-title {
    font-size: 1.3em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

/* 模型对比 */
.model-comparison {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.model-card {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.model-card:hover {
    transform: translateY(-5px);
}

.teacher-model {
    border-left: 5px solid #e74c3c;
}

.student-model {
    border-left: 5px solid #27ae60;
}

.model-header {
    text-align: center;
    margin-bottom: 15px;
}

.model-header h4 {
    font-size: 1.1em;
    margin-bottom: 5px;
    color: #2c3e50;
}

.model-size {
    font-size: 0.9em;
    color: #7f8c8d;
    font-weight: 600;
}

/* 模型大脑可视化 */
.model-brain {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.brain-layers {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.layer {
    height: 8px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    animation: pulse-layer 2s infinite;
}

.teacher-model .layer {
    width: 80px;
}

.student-model .layer {
    width: 50px;
}

@keyframes pulse-layer {

    0%,
    100% {
        opacity: 0.7;
    }

    50% {
        opacity: 1;
    }
}

/* 模型统计 */
.model-stats {
    margin-top: 15px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 0.9em;
    font-weight: 600;
    color: #5a6c7d;
    min-width: 60px;
}

.stat-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.5s ease;
}

.teacher-accuracy {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.teacher-speed {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.teacher-resource {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.student-accuracy {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.student-speed {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.student-resource {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.stat-value {
    font-size: 0.8em;
    font-weight: 600;
    color: #5a6c7d;
    min-width: 30px;
}

/* 蒸馏箭头 */
.distillation-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.arrow-body {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
    text-align: center;
    position: relative;
    margin-bottom: 10px;
}

.arrow-text {
    display: block;
    margin-bottom: 5px;
}

.knowledge-particles {
    display: flex;
    justify-content: center;
    gap: 3px;
}

.particle {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: flow 1.5s infinite;
}

.particle:nth-child(2) {
    animation-delay: 0.3s;
}

.particle:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes flow {

    0%,
    100% {
        transform: translateX(0) scale(1);
        opacity: 0.8;
    }

    50% {
        transform: translateX(10px) scale(1.2);
        opacity: 1;
    }
}

.arrow-head {
    font-size: 2em;
    color: #667eea;
    animation: bounce 2s infinite;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateX(0);
    }

    50% {
        transform: translateX(5px);
    }
}

/* 温度演示 */
.temperature-demo {
    margin-bottom: 30px;
}

.temperature-control {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.temperature-control label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
}

.temperature-control input[type="range"] {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.temperature-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    cursor: pointer;
}

#temperature-value {
    font-weight: 600;
    color: #667eea;
    min-width: 40px;
    text-align: center;
}

.prediction-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.prediction-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.prediction-card h4 {
    text-align: center;
    margin-bottom: 15px;
    color: #2c3e50;
}

.logits-display,
.probability-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.logit-bar,
.prob-bar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 80px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.5s ease;
}

.value {
    font-weight: 600;
    color: #5a6c7d;
    min-width: 40px;
    text-align: right;
}

.temperature-explanation {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.explanation-card {
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.low-temp {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.high-temp {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.explanation-card h5 {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.explanation-card p {
    font-size: 0.9em;
    line-height: 1.4;
    margin-bottom: 5px;
}

/* 蒸馏过程 */
.distillation-process {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.process-step {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.data-flow {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.data-item {
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
    color: #5a6c7d;
    border: 2px solid #e9ecef;
}

.teacher-inference {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-data {
    background: #e3f2fd;
    padding: 10px 15px;
    border-radius: 10px;
    font-weight: 600;
    color: #1976d2;
}

.teacher-output {
    display: flex;
    gap: 10px;
}

.soft-target,
.hard-target {
    flex: 1;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.9em;
    font-weight: 600;
    text-align: center;
}

.soft-target {
    background: #fff3e0;
    color: #f57c00;
}

.hard-target {
    background: #f3e5f5;
    color: #7b1fa2;
}

.student-training {
    width: 100%;
}

.loss-components {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.loss-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.loss-label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 80px;
    font-size: 0.9em;
}

.loss-bar {
    flex: 1;
    height: 15px;
    background: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.loss-fill {
    height: 100%;
    border-radius: 8px;
    transition: width 0.5s ease;
}

.loss-fill.distillation {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.loss-fill.task {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.evaluation-metrics {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.metric-name {
    font-weight: 600;
    color: #2c3e50;
}

.metric-value {
    font-weight: 600;
    color: #667eea;
}

/* 控制按钮 */
.demo-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.core-code-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.core-code-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226) !important;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3) !important;
}

.btn-icon {
    font-size: 1.1em;
}

/* 知识面板 */
.knowledge-panel {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
}

.knowledge-title {
    font-size: 1.3em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.knowledge-section {
    margin-bottom: 25px;
}

.knowledge-section h4 {
    font-size: 1.1em;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-section p {
    line-height: 1.6;
    color: #5a6c7d;
    margin-bottom: 10px;
}

/* 类比框 */
.analogy-box {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
}

.analogy-box h5 {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.analogy-box p {
    margin-bottom: 8px;
    color: white;
}

/* 公式框 */
.formula-box {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
}

.formula {
    font-family: 'Courier New', monospace;
    font-size: 1.1em;
    font-weight: bold;
    text-align: center;
    margin: 10px 0;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    border-radius: 5px;
}

.formula-desc {
    font-size: 0.9em;
    text-align: center;
    opacity: 0.9;
    font-style: italic;
}

/* 列表样式 */
.feature-list,
.application-list,
.tips-list,
.process-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li,
.application-list li,
.tips-list li {
    padding: 8px 0;
    border-left: 3px solid #667eea;
    padding-left: 15px;
    margin-bottom: 8px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 0 5px 5px 0;
}

.process-list {
    counter-reset: step-counter;
}

.process-list li {
    counter-increment: step-counter;
    padding: 10px 0;
    padding-left: 40px;
    position: relative;
    margin-bottom: 10px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 5px;
    padding-right: 15px;
}

.process-list li::before {
    content: counter(step-counter);
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: 600;
}

/* 优势网格 */
.advantage-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.advantage-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.advantage-item:hover {
    transform: translateY(-3px);
}

.advantage-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
}

.advantage-item h5 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1em;
}

.advantage-item p {
    color: #5a6c7d;
    font-size: 0.9em;
    margin: 0;
}

/* 温度类比 */
.temperature-analogy {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.temp-state {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.temp-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
}

.temp-state h5 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1em;
}

.temp-state p {
    color: #5a6c7d;
    font-size: 0.9em;
    margin-bottom: 5px;
}

/* 底部 */
.footer {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    color: #5a6c7d;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.footer p {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .main-title {
        font-size: 2em;
    }

    .demo-tabs {
        gap: 10px;
    }

    .tab-btn {
        padding: 12px 20px;
        font-size: 0.9em;
    }

    .model-comparison {
        flex-direction: column;
        gap: 15px;
    }

    .distillation-arrow {
        flex-direction: row;
        min-width: auto;
    }

    .arrow-body {
        margin-bottom: 0;
        margin-right: 10px;
    }

    .prediction-comparison {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .temperature-explanation {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .advantage-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .temperature-analogy {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        padding: 30px 20px;
    }

    .main-title {
        font-size: 1.8em;
        flex-direction: column;
        gap: 10px;
    }

    .demo-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .main-content {
        padding: 20px;
    }

    .knowledge-panel {
        max-height: none;
    }

    .process-step {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .temperature-control {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .temperature-control label {
        min-width: auto;
        text-align: center;
    }
}

/* 滚动条样式 */
.knowledge-panel::-webkit-scrollbar {
    width: 8px;
}

.knowledge-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.knowledge-panel::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

.knowledge-panel::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* 核心代码模态框样式 */
.code-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.code-modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.code-modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-modal-header h3 {
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
}

.code-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 2em;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.code-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.code-modal-body {
    padding: 30px;
    max-height: calc(90vh - 100px);
    overflow-y: auto;
}

.code-description {
    font-size: 1.1em;
    color: #5a6c7d;
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
}

.code-container {
    background: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.code-header {
    background: #2d2d2d;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #404040;
}

.code-language {
    color: #ffd700;
    font-weight: 600;
    font-size: 0.9em;
}

.copy-code-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-code-btn:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-1px);
}

.code-content {
    background: #1a1a1a;
    color: #e2e8f0;
    padding: 25px;
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.6;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.code-content code {
    color: #e2e8f0;
}

/* 模态框动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 模态框响应式设计 */
@media (max-width: 768px) {
    .code-modal-content {
        width: 95%;
        max-height: 95%;
    }

    .code-modal-header {
        padding: 15px 20px;
    }

    .code-modal-header h3 {
        font-size: 1.1em;
    }

    .code-modal-body {
        padding: 20px;
    }

    .code-content {
        padding: 20px;
        font-size: 0.8em;
    }

    .code-header {
        padding: 12px 15px;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .copy-code-btn {
        width: 100%;
        text-align: center;
    }
}

/* 新增动画效果 */
@keyframes knowledge-flow {
    0% {
        transform: translateX(-10px) scale(1);
        opacity: 0.8;
    }

    50% {
        transform: translateX(15px) scale(1.3);
        opacity: 1;
    }

    100% {
        transform: translateX(-10px) scale(1);
        opacity: 0.8;
    }
}

@keyframes pulse-text {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes pulse-glow {

    0%,
    100% {
        box-shadow: 0 0 5px rgba(25, 118, 210, 0.3);
        transform: scale(1);
    }

    50% {
        box-shadow: 0 0 20px rgba(25, 118, 210, 0.6);
        transform: scale(1.02);
    }
}

/* 温度提示框样式 */
.temperature-tooltip {
    position: absolute;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 15px;
    border-radius: 10px;
    font-size: 0.9em;
    font-weight: 600;
    z-index: 1000;
    animation: tooltip-appear 0.5s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

@keyframes tooltip-appear {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 成功消息样式 */
.success-message {
    margin-top: 20px;
    animation: success-appear 0.5s ease;
}

@keyframes success-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 增强现有动画 */
.model-card {
    transition: all 0.3s ease;
}

.process-step {
    transition: all 0.3s ease;
}

.data-item {
    transition: all 0.3s ease;
}

.metric {
    transition: all 0.3s ease;
}

/* 添加悬停效果增强交互感 */
.control-btn:active {
    transform: translateY(0) scale(0.98);
}

.tab-btn:active {
    transform: translateY(0) scale(0.98);
}

/* 增强粒子动画 */
.particle {
    transition: all 0.3s ease;
}

/* 增强统计条动画 */
.stat-fill {
    transition: all 0.8s ease;
}

.loss-fill {
    transition: all 0.5s ease;
}

/* 场景信息样式 */
.scenario-info {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 18px;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.scenario-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.scenario-icon {
    font-size: 1.8em;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.scenario-title {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.2em;
}

.question-box {
    background: white;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #667eea;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.question-box h5 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.question-text {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 12px;
    font-style: italic;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    font-size: 1.05em;
}

.input-hint {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
    font-size: 0.95em;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
}

.hint-icon {
    font-size: 1.2em;
}

.hint-text {
    font-style: italic;
    font-weight: 500;
}

/* Logits解释样式 */
.logits-explanation {
    margin-top: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e8f5e8);
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #28a745;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.explanation-text {
    color: #495057;
    font-size: 0.95em;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.explanation-icon {
    font-size: 1.2em;
}

/* 软硬目标对比样式 */
.targets-comparison {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.scenario-description {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #dee2e6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.scenario-card h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.question-display {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.question-text {
    font-size: 1.1em;
    color: #2c3e50;
    margin-bottom: 20px;
    line-height: 1.6;
}

.options-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.option-item.correct {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.option-label {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9em;
}

.option-item.correct .option-label {
    background: #28a745;
}

.option-text {
    flex: 1;
    color: #2c3e50;
    font-weight: 500;
}

.correct-mark {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 对比展示样式 */
.comparison-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.target-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.target-card.hard-target {
    border-color: #6c757d;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.target-card.soft-target {
    border-color: #667eea;
    background: linear-gradient(135deg, #ffffff, #f0f4ff);
}

.target-card h4 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.target-card.hard-target h4 {
    color: #6c757d;
}

.target-card.soft-target h4 {
    color: #667eea;
}

.target-icon {
    font-size: 1.3em;
}

.target-explanation {
    margin-bottom: 20px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    font-style: italic;
    color: #6c757d;
}

.target-values {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.value-row {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.value-row.correct {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #28a745;
}

.value-row .option-label {
    width: 25px;
    height: 25px;
    font-size: 0.8em;
}

.value-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.value-bar.hard .bar-fill {
    background: linear-gradient(90deg, #6c757d, #495057);
}

.value-bar.soft .bar-fill {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.bar-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.value-number {
    font-weight: 600;
    color: #2c3e50;
    min-width: 40px;
    text-align: right;
}

.correct-indicator {
    color: #28a745;
    font-weight: 600;
}

.insight {
    font-size: 0.8em;
    color: #6c757d;
    font-style: italic;
    margin-left: 8px;
}

.target-summary {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    padding: 12px;
}

.summary-text {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: #495057;
    font-size: 0.95em;
}

.summary-icon {
    font-size: 1.1em;
}

/* 学习效果对比样式 */
.learning-effects {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #dee2e6;
}

.learning-effects h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-align: center;
}

.effects-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.effect-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.effect-card:first-child {
    border-color: #6c757d;
}

.effect-card:last-child {
    border-color: #667eea;
}

.effect-card h5 {
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.effect-card:first-child h5 {
    color: #6c757d;
}

.effect-card:last-child h5 {
    color: #667eea;
}

.effect-list {
    list-style: none;
    padding: 0;
    margin-bottom: 20px;
}

.effect-list li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    color: #495057;
    line-height: 1.5;
}

.effect-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
    font-size: 1.2em;
}

.effect-score {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
}

.score-label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.score-bar {
    flex: 1;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 0.8s ease;
}

.score-fill.basic {
    background: linear-gradient(90deg, #6c757d, #495057);
}

.score-fill.advanced {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.score-text {
    font-weight: 600;
    color: #2c3e50;
    min-width: 50px;
}

/* 知识点容器样式 */
.knowledge-container {
    background: linear-gradient(135deg, #f0f4ff, #e6f3ff);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #667eea;
    margin-top: 30px;
}

.knowledge-container h4 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.knowledge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.knowledge-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
    border: 1px solid #e6f3ff;
    transition: all 0.3s ease;
}

.knowledge-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.knowledge-card h5 {
    color: #667eea;
    margin-bottom: 12px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-card p {
    color: #495057;
    line-height: 1.6;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .comparison-display {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .effects-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .options-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .targets-comparison {
        gap: 20px;
    }

    .scenario-description,
    .learning-effects,
    .knowledge-container {
        padding: 20px;
    }

    .target-card {
        padding: 20px;
    }
}
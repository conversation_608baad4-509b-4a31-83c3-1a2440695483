/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    padding: 20px;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 3em;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.header p {
    font-size: 1.3em;
    color: #666;
    line-height: 1.6;
}

/* 控制面板样式 */
.control-panel {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 50px;
    border: 2px solid #dee2e6;
}

.control-panel h3 {
    color: #1e3c72;
    margin-bottom: 20px;
    font-size: 1.8em;
    text-align: center;
}

.input-section {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
}

.input-box {
    padding: 15px 20px;
    border: 2px solid #1e3c72;
    border-radius: 15px;
    font-size: 1.1em;
    outline: none;
    transition: all 0.3s ease;
}

.input-box:focus {
    border-color: #2a5298;
    box-shadow: 0 0 20px rgba(30, 60, 114, 0.3);
}

.process-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
}

.process-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(30, 60, 114, 0.4);
}

.process-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.button-group {
    text-align: center;
    margin-top: 15px;
}

.secondary-btn,
.info-btn {
    padding: 10px 20px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.secondary-btn {
    background: #f8f9fa;
    color: #333;
}

.secondary-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.info-btn {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.info-btn:hover {
    background: #bbdefb;
    transform: translateY(-2px);
}

/* MoE架构可视化样式 */
.moe-architecture {
    position: relative;
    background: radial-gradient(circle at center, #f0f8ff, #e6f3ff);
    border-radius: 25px;
    padding: 50px 40px;
    min-height: 800px;
    border: 3px solid #1e3c72;
    margin-bottom: 40px;
    overflow: hidden;
}

.architecture-header {
    text-align: center;
    margin-bottom: 50px;
}

.architecture-title {
    font-size: 2.2em;
    color: #1e3c72;
    font-weight: 800;
    margin-bottom: 10px;
}

.architecture-subtitle {
    font-size: 1.1em;
    color: #666;
    font-style: italic;
}

/* 输入层样式 */
.input-layer {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 20px 30px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.2em;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
    z-index: 10;
    min-width: 200px;
    text-align: center;
}

/* 路由器样式 */
.router {
    position: absolute;
    top: 220px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffc107, #ff8f00);
    color: white;
    padding: 25px;
    border-radius: 50%;
    font-weight: 800;
    font-size: 1.1em;
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.4);
    z-index: 10;
    width: 120px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* 专家模块样式 */
.experts-container {
    position: absolute;
    top: 380px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 30px;
    z-index: 8;
}

.expert {
    background: white;
    border: 3px solid #6c757d;
    border-radius: 20px;
    padding: 20px;
    width: 150px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.expert.active {
    border-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5, #ffe6e6);
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(220, 53, 69, 0.3);
}

.expert.selected {
    border-color: #007bff;
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.expert-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.expert-name {
    font-weight: 700;
    color: #333;
    font-size: 0.9em;
}

.expert-score {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: 700;
    opacity: 0;
    transition: all 0.3s ease;
}

.expert.scored .expert-score {
    opacity: 1;
}

/* 输出层样式 */
.output-layer {
    position: absolute;
    top: 550px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
    color: white;
    padding: 20px 30px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1em;
    line-height: 1.6;
    box-shadow: 0 10px 30px rgba(111, 66, 193, 0.3);
    z-index: 10;
    min-width: 300px;
    max-width: 500px;
    text-align: left;
    opacity: 0;
    transition: all 0.5s ease;
    white-space: pre-line;
}

.output-layer.show {
    opacity: 1;
}

/* 连接线样式 */
.connection-line {
    position: absolute;
    height: 3px;
    border-radius: 2px;
    z-index: 5;
    opacity: 0;
    transition: all 0.5s ease;
    transform-origin: left center;
}

.connection-line.active {
    opacity: 1;
    animation: flow 2s infinite;
}

/* 精确连接线布局 - 真正连接路由器和专家的边缘 */
#line1 {
    /* 输入层到路由器顶部 */
    top: 200px;
    left: calc(50% - 1.5px);
    width: 3px;
    height: 20px;
    background: linear-gradient(180deg, #28a745, #ffc107);
}

/* 路由器中心: (50%, 280px), 半径: 60px */
/* 专家容器: top: 380px, 专家宽度: 150px, 间距: 30px */
/* 专家1中心: (50% - 180px, 440px) */
/* 专家2中心: (50% - 90px, 440px) */
/* 专家3中心: (50%, 440px) */
/* 专家4中心: (50% + 90px, 440px) */
/* 专家5中心: (50% + 180px, 440px) */

#line2 {
    /* 路由器到专家1 - 精确计算的连接 */
    top: 315px;
    left: calc(50% - 35px);
    width: 110px;
    height: 3px;
    transform: rotate(-25deg);
    background: linear-gradient(90deg, #ffc107, #007bff);
}

#line3 {
    /* 路由器到专家2 - 精确计算的连接 */
    top: 330px;
    left: calc(50% - 20px);
    width: 75px;
    height: 3px;
    transform: rotate(-12deg);
    background: linear-gradient(90deg, #ffc107, #007bff);
}

#line4 {
    /* 路由器到专家3 - 垂直连接 */
    top: 340px;
    left: calc(50% - 1.5px);
    width: 3px;
    height: 40px;
    background: linear-gradient(180deg, #ffc107, #007bff);
}

#line5 {
    /* 路由器到专家4 - 精确计算的连接 */
    top: 330px;
    left: calc(50% - 55px);
    width: 75px;
    height: 3px;
    transform: rotate(12deg);
    background: linear-gradient(90deg, #ffc107, #007bff);
}

#line6 {
    /* 路由器到专家5 - 精确计算的连接 */
    top: 315px;
    left: calc(50% - 75px);
    width: 110px;
    height: 3px;
    transform: rotate(25deg);
    background: linear-gradient(90deg, #ffc107, #007bff);
}

#line7 {
    /* 专家层到输出层 */
    top: 500px;
    left: calc(50% - 1.5px);
    width: 3px;
    height: 50px;
    background: linear-gradient(180deg, #007bff, #6f42c1);
}

@keyframes flow {
    0% {
        background: linear-gradient(90deg, transparent, #1e3c72, transparent);
    }

    50% {
        background: linear-gradient(90deg, transparent, #dc3545, transparent);
    }

    100% {
        background: linear-gradient(90deg, transparent, #1e3c72, transparent);
    }
}

/* 实时解释面板样式 */
.live-explanation {
    background: linear-gradient(135deg, #e8f5e8, #f0fff0);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border-left: 6px solid #28a745;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.1);
}

.live-explanation h3 {
    color: #155724;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-align: center;
}

.explanation-content {
    max-height: 200px;
    overflow: hidden;
}

.explanation-item {
    display: none;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
}

.explanation-item.active {
    display: flex;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.explanation-icon {
    font-size: 3em;
    min-width: 60px;
    text-align: center;
}

.explanation-text {
    flex: 1;
    font-size: 1.1em;
    line-height: 1.6;
}

.explanation-text strong {
    color: #155724;
    font-size: 1.2em;
    display: block;
    margin-bottom: 8px;
}

/* 详细说明面板样式 */
.explanation-panel {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-radius: 20px;
    padding: 30px;
    border-left: 6px solid #ffc107;
    margin-top: 30px;
}

.explanation-panel h3 {
    color: #856404;
    margin-bottom: 30px;
    font-size: 1.8em;
    text-align: center;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.advantage-item {
    background: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid #f8f9fa;
    min-height: 180px;
}

.advantage-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #ffc107;
}

.advantage-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.advantage-item h4 {
    color: #856404;
    font-size: 1.3em;
    margin-bottom: 15px;
    font-weight: 700;
}

.advantage-item p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9em;
}

/* 静态图示样式 */
.moe-diagram {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    padding: 40px;
    margin-top: 40px;
    border: 2px solid #dee2e6;
    text-align: center;
}

.moe-diagram h3 {
    color: #1e3c72;
    margin-bottom: 30px;
    font-size: 1.8em;
}

.diagram-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.diagram-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.diagram-input {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    font-weight: 700;
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
}

.diagram-router {
    background: linear-gradient(135deg, #ffc107, #ff8f00);
    color: white;
    padding: 20px;
    border-radius: 50%;
    font-weight: 800;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
}

.diagram-experts {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.diagram-expert {
    background: white;
    border: 2px solid #6c757d;
    border-radius: 10px;
    padding: 8px 15px;
    font-size: 0.9em;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.diagram-expert.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    transform: scale(1.05);
}

.diagram-output {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    font-weight: 700;
    box-shadow: 0 8px 20px rgba(111, 66, 193, 0.3);
}

.diagram-arrow {
    font-size: 2em;
    color: #1e3c72;
    font-weight: bold;
}

.diagram-flow {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 20px 0;
    justify-content: center;
    flex-wrap: wrap;
}

.flow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 120px;
}

.flow-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    color: white;
}

.flow-step:nth-child(1) .flow-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.flow-step:nth-child(3) .flow-icon {
    background: linear-gradient(135deg, #ffc107, #ff8f00);
}

.flow-step:nth-child(5) .flow-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.flow-step:nth-child(7) .flow-icon {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
}

.flow-label {
    font-size: 0.9em;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.concept-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 30px;
}

.concept-item {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #1e3c72;
}

.concept-title {
    font-size: 1.2em;
    font-weight: 700;
    color: #1e3c72;
    margin-bottom: 10px;
}

.concept-desc {
    color: #666;
    line-height: 1.5;
    font-size: 0.95em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
    }

    .header h1 {
        font-size: 2.2em;
    }

    .input-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .experts-container {
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
    }

    .expert {
        width: 120px;
        height: 100px;
    }

    .moe-architecture {
        min-height: 600px;
        padding: 30px 20px;
    }

    .architecture-title {
        font-size: 1.8em;
    }

    .advantages-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .diagram-container {
        gap: 20px;
    }

    .diagram-flow {
        gap: 15px;
    }

    .concept-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .explanation-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .explanation-icon {
        font-size: 2.5em;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8em;
    }

    .control-panel h3 {
        font-size: 1.4em;
    }

    .architecture-title {
        font-size: 1.5em;
    }

    .expert {
        width: 100px;
        height: 90px;
        padding: 15px;
    }

    .expert-icon {
        font-size: 2em;
    }

    .expert-name {
        font-size: 0.8em;
    }

    .advantages-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .diagram-container {
        flex-direction: column;
        gap: 15px;
    }

    .diagram-flow {
        flex-direction: column;
        gap: 10px;
    }

    .flow-step {
        min-width: 100px;
    }

    .flow-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2em;
    }
}
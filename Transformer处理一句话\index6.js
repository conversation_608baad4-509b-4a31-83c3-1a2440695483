// 演示数据和函数
const demoData = {
    embedding: {
        title: "🔍 输入嵌入演示",
        description: "观察文本如何转换为数字向量",
        interactive: true
    },
    positional: {
        title: "📍 位置编码演示",
        description: "理解位置信息如何添加到词向量中",
        interactive: true
    },
    attention: {
        title: "🎯 多头注意力演示",
        description: "可视化词汇间的注意力权重",
        interactive: true
    },
    feedforward: {
        title: "⚡ 前馈网络演示",
        description: "观察神经网络的前向传播过程",
        interactive: true
    },
    normalization: {
        title: "🔄 归一化与残差连接演示",
        description: "理解层归一化和残差连接的作用",
        interactive: true
    },
    output: {
        title: "📊 输出预测演示",
        description: "查看模型如何预测下一个词",
        interactive: true
    }
};

function openDemo(type) {
    const modal = document.getElementById('demoModal');
    const content = document.getElementById('modalContent');
    const demo = demoData[type];

    content.innerHTML = createDemoContent(type, demo);
    modal.style.display = 'block';

    // 添加特定演示的交互功能
    initializeDemoInteractions(type);
}

function closeDemo() {
    document.getElementById('demoModal').style.display = 'none';
    // 确保背景滚动恢复
    document.body.style.overflow = 'auto';
}

function createDemoContent(type, demo) {
    return `
                <h2>${demo.title}</h2>
                <p style="margin-bottom: 20px; color: #666;">${demo.description}</p>
                <div class="demo-area">
                    <input type="text" class="input-sentence" id="demoInput" 
                           placeholder="请输入一句话，例如：我喜欢学习人工智能" 
                           value="我喜欢学习人工智能">
                    <button class="process-btn" onclick="processDemo('${type}')">开始处理</button>
                    <button class="process-btn" onclick="clearDemo()" style="background: #dc3545;">清除结果</button>
                    <div class="result-area" id="demoResult">
                        <p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>
                    </div>
                </div>
            `;
}

function initializeDemoInteractions(type) {
    // 为每种演示类型初始化特定的交互功能
    const input = document.getElementById('demoInput');
    input.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            processDemo(type);
        }
    });
}

function processDemo(type) {
    const input = document.getElementById('demoInput').value;
    const result = document.getElementById('demoResult');

    if (!input.trim()) {
        result.innerHTML = '<p style="color: #dc3545;">请输入一句话！</p>';
        return;
    }

    // 根据不同类型生成不同的演示效果
    switch (type) {
        case 'embedding':
            showEmbeddingDemo(input, result);
            break;
        case 'positional':
            showPositionalDemo(input, result);
            break;
        case 'attention':
            showAttentionDemo(input, result);
            break;
        case 'feedforward':
            showFeedForwardDemo(input, result);
            break;
        case 'normalization':
            showNormalizationDemo(input, result);
            break;
        case 'output':
            showOutputDemo(input, result);
            break;
    }
}

function clearDemo() {
    document.getElementById('demoResult').innerHTML =
        '<p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>';
}

// 点击模态框外部关闭
window.onclick = function (event) {
    const modal = document.getElementById('demoModal');
    if (event.target === modal) {
        closeDemo();
    }
}

// 具体演示函数实现
function showEmbeddingDemo(input, result) {
    const tokens = input.split('');
    let html = '<h4>🔍 词汇嵌入演示 - 3D向量空间可视化</h4>';

    // 创建3D向量空间可视化
    html += '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin: 15px 0; color: white;">';
    html += '<h5 style="margin-bottom: 15px;">🌌 词汇在高维空间中的位置</h5>';
    html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

    tokens.forEach((token, index) => {
        const x = (Math.random() * 200 - 100).toFixed(1);
        const y = (Math.random() * 200 - 100).toFixed(1);
        const z = (Math.random() * 200 - 100).toFixed(1);
        const similarity = Math.random();
        const color = `hsl(${similarity * 360}, 70%, 80%)`;

        html += `
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; border: 2px solid ${color};">
                    <div style="font-size: 1.5em; text-align: center; margin-bottom: 10px;">${token}</div>
                    <div style="font-family: monospace; font-size: 0.9em;">
                        <div>X: ${x}</div>
                        <div>Y: ${y}</div>
                        <div>Z: ${z}</div>
                    </div>
                    <div style="margin-top: 8px; font-size: 0.8em; opacity: 0.8;">
                        语义相似度: ${(similarity * 100).toFixed(1)}%
                    </div>
                </div>`;
    });

    html += '</div>';
    html += '<div style="margin-top: 15px; font-size: 0.9em; opacity: 0.9;">';
    html += '💡 相似颜色的词汇在语义空间中距离更近，表示含义相关';
    html += '</div>';
    html += '</div>';

    // 词汇相似度矩阵
    html += '<div style="margin: 20px 0;"><h5>📊 词汇相似度热力图</h5></div>';
    html += '<div style="display: grid; grid-template-columns: 50px repeat(' + tokens.length + ', 40px); gap: 2px; margin: 10px 0;">';

    // 表头
    html += '<div></div>';
    tokens.forEach(token => {
        html += `<div style="text-align: center; font-weight: bold; font-size: 0.8em;">${token}</div>`;
    });

    // 相似度矩阵
    for (let i = 0; i < tokens.length; i++) {
        html += `<div style="writing-mode: vertical-rl; text-align: center; font-weight: bold; font-size: 0.8em;">${tokens[i]}</div>`;
        for (let j = 0; j < tokens.length; j++) {
            const similarity = i === j ? 1 : Math.random() * 0.8 + 0.1;
            const intensity = Math.floor(similarity * 255);
            const color = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
            html += `<div style="background: ${color}; color: ${intensity > 128 ? 'white' : 'black'};
                        width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;
                        border-radius: 4px; font-size: 0.7em; font-weight: bold;">
                        ${similarity.toFixed(2)}
                    </div>`;
        }
    }

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">';
    html += '<strong>� 技术原理：</strong>每个字符被映射到512维向量空间，通过余弦相似度计算词汇间的语义距离。相似的词汇在空间中聚集，不同的词汇分散分布。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 15px; color: white;">
            <h4 style="color: #fff; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示文本如何从离散的符号转换为连续的数值向量，这是所有深度学习模型处理文本的第一步。
                    通过3D可视化帮助理解高维向量空间中词汇的分布特性。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>分布式表示：</strong>每个词用稠密向量表示，相似词在向量空间中距离更近<br>
                    • <strong>维度映射：</strong>将离散的词汇表映射到连续的高维空间（通常512或1024维）<br>
                    • <strong>语义编码：</strong>向量的每个维度都捕获词汇的某种语义特征
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    嵌入向量是<strong>可学习参数</strong>，在训练过程中会不断调整以更好地表示词汇的语义关系。
                    相同的词在不同上下文中使用相同的嵌入向量，这是静态嵌入的特点（与上下文相关的动态表示在后续层中产生）。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

function showPositionalDemo(input, result) {
    const tokens = input.split('');
    let html = '<h4>📍 位置编码过程：</h4>';

    html += '<div style="margin: 15px 0;"><strong>1. 原始词向量 + 位置编码：</strong></div>';
    html += '<div style="display: grid; gap: 10px;">';

    tokens.forEach((token, index) => {
        const pos = index + 1;
        const posEncoding = Math.sin(pos / 10000).toFixed(3);
        html += `
                <div style="display: flex; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                    <div style="flex: 1;">
                        <strong>位置 ${pos}:</strong> "${token}"
                    </div>
                    <div style="flex: 2; font-family: monospace;">
                        词向量 + 位置编码(${posEncoding})
                    </div>
                    <div style="width: 100px; height: 20px; background: linear-gradient(90deg,
                        hsl(${(pos * 30) % 360}, 70%, 80%),
                        hsl(${(pos * 30 + 60) % 360}, 70%, 80%));
                        border-radius: 10px;"></div>
                </div>`;
    });

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>位置编码使用正弦和余弦函数，让模型能够理解词汇的相对位置关系。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white;">
            <h4 style="color: #fff; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示如何为序列中的每个位置添加位置信息，解决Transformer架构中缺乏位置感知能力的问题。
                    通过正弦和余弦函数的组合，为模型提供绝对和相对位置信息。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>位置不变性问题：</strong>纯注意力机制无法区分词汇的顺序<br>
                    • <strong>正弦位置编码：</strong>使用不同频率的正弦和余弦函数编码位置<br>
                    • <strong>相对位置感知：</strong>模型可以学习到词汇间的相对距离关系
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    位置编码与词嵌入<strong>相加而非拼接</strong>，这要求两者维度相同。
                    正弦位置编码的优势是可以处理训练时未见过的序列长度，具有良好的外推能力。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

function showAttentionDemo(input, result) {
    const tokens = input.split('');
    const n = tokens.length;

    let html = '<h4>🎯 多头注意力机制：</h4>';
    html += '<div style="margin: 15px 0;"><strong>注意力权重矩阵（模拟）：</strong></div>';

    // 创建注意力矩阵
    html += '<div style="display: grid; grid-template-columns: 50px repeat(' + n + ', 40px); gap: 2px; margin: 10px 0;">';

    // 表头
    html += '<div></div>';
    tokens.forEach(token => {
        html += `<div style="text-align: center; font-weight: bold; font-size: 0.8em;">${token}</div>`;
    });

    // 注意力矩阵
    for (let i = 0; i < n; i++) {
        html += `<div style="writing-mode: vertical-rl; text-align: center; font-weight: bold; font-size: 0.8em;">${tokens[i]}</div>`;
        for (let j = 0; j < n; j++) {
            const attention = Math.random();
            const intensity = Math.floor(attention * 255);
            const color = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
            html += `<div class="attention-cell" style="background: ${color}; color: ${intensity > 128 ? 'white' : 'black'};">
                        ${attention.toFixed(2)}
                    </div>`;
        }
    }

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>颜色越深表示注意力权重越高，显示了每个字符对其他字符的关注程度。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%); border-radius: 15px; color: white;">
            <h4 style="color: #fff; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示注意力机制如何让模型动态地关注序列中的不同部分，实现上下文相关的表示学习。
                    多头设计允许模型同时关注不同类型的语言关系（语法、语义、长距离依赖等）。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>注意力权重：</strong>通过Query、Key、Value机制计算词汇间的相关性<br>
                    • <strong>多头并行：</strong>不同的头关注不同的语言现象和关系类型<br>
                    • <strong>自注意力：</strong>序列内部元素之间的相互关注和信息交换
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    注意力权重矩阵是<strong>动态计算</strong>的，每个输入序列都会产生不同的注意力模式。
                    多头注意力的精髓在于<strong>表示子空间的多样性</strong>，每个头在不同的表示子空间中捕获不同的关系模式。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

function showFeedForwardDemo(input, result) {
    const tokens = input.split('');

    let html = '<h4>⚡ 前馈神经网络处理：</h4>';
    html += '<div style="margin: 15px 0;"><strong>网络层级处理过程：</strong></div>';

    tokens.forEach((token, index) => {
        html += `
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #4CAF50;">
                    <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的处理过程：</div>
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="padding: 8px 12px; background: #e3f2fd; border-radius: 5px; font-size: 0.9em;">
                            输入层<br><small>[512维]</small>
                        </div>
                        <div style="font-size: 1.5em;">→</div>
                        <div style="padding: 8px 12px; background: #fff3e0; border-radius: 5px; font-size: 0.9em;">
                            隐藏层<br><small>[2048维]</small>
                        </div>
                        <div style="font-size: 1.5em;">→</div>
                        <div style="padding: 8px 12px; background: #e8f5e8; border-radius: 5px; font-size: 0.9em;">
                            输出层<br><small>[512维]</small>
                        </div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        激活函数: ReLU | 参数量: ${(512 * 2048 + 2048 * 512).toLocaleString()}
                    </div>
                </div>`;
    });

    html += '<div style="margin-top: 15px; padding: 10px; background: #fff8e1; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>前馈网络对每个位置独立处理，通过非线性变换增强特征表达能力。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); border-radius: 15px; color: #333;">
            <h4 style="color: #333; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示前馈网络如何对注意力层的输出进行非线性变换，增强模型的表达能力。
                    通过两层全连接网络和激活函数，实现复杂的特征变换和信息整合。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>非线性变换：</strong>通过ReLU等激活函数引入非线性，增强模型表达能力<br>
                    • <strong>维度扩展：</strong>中间层维度通常是输入的4倍（如512→2048→512）<br>
                    • <strong>位置独立：</strong>每个位置的向量独立处理，保持序列结构
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    前馈网络是Transformer中<strong>唯一的非线性变换</strong>模块，对模型的表达能力至关重要。
                    中间层的高维度设计允许模型学习更复杂的特征组合和变换模式。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

function showNormalizationDemo(input, result) {
    const tokens = input.split('');

    let html = '<h4>🔄 层归一化与残差连接：</h4>';

    tokens.forEach((token, index) => {
        const beforeNorm = Array.from({ length: 4 }, () => (Math.random() * 10 - 5).toFixed(2));
        const afterNorm = beforeNorm.map(x => ((x - 0) / 2.5).toFixed(2));

        html += `
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                    <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的归一化过程：</div>

                    <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
                        <div>
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">归一化前:</div>
                            <div style="font-family: monospace; background: #ffebee; padding: 8px; border-radius: 4px;">
                                [${beforeNorm.join(', ')}]
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <div style="background: #2196F3; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em;">
                                LayerNorm
                            </div>
                        </div>

                        <div>
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">归一化后:</div>
                            <div style="font-family: monospace; background: #e8f5e8; padding: 8px; border-radius: 4px;">
                                [${afterNorm.join(', ')}]
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; font-size: 0.9em;">
                        <strong>残差连接:</strong> 输出 = LayerNorm(输入 + 子层输出)
                    </div>
                </div>`;
    });

    html += '<div style="margin-top: 15px; padding: 10px; background: #f3e5f5; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>层归一化稳定训练，残差连接帮助梯度传播，防止梯度消失问题。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border-radius: 15px; color: #333;">
            <h4 style="color: #333; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示层归一化如何稳定训练过程，以及残差连接如何解决深度网络中的梯度消失问题。
                    这两个技术是训练深度Transformer模型的关键组件。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>层归一化：</strong>对每个样本的特征维度进行标准化，稳定训练<br>
                    • <strong>残差连接：</strong>将输入直接加到输出上，形成恒等映射的快捷路径<br>
                    • <strong>梯度流动：</strong>残差连接确保梯度能够直接传播到较早的层
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    层归一化在<strong>特征维度</strong>上进行（与批归一化在批次维度不同），使其适用于序列数据。
                    残差连接的<strong>Add&Norm</strong>结构是现代深度学习架构的标准设计模式。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

function showOutputDemo(input, result) {
    const tokens = input.split('');
    const vocab = ['我', '你', '他', '喜', '欢', '学', '习', '人', '工', '智', '能', '的', '是', '在', '有'];

    let html = '<h4>📊 输出预测过程：</h4>';
    html += '<div style="margin: 15px 0;"><strong>预测下一个字符的概率分布：</strong></div>';

    // 生成概率分布
    const probabilities = vocab.map(() => Math.random()).sort((a, b) => b - a);
    const total = probabilities.reduce((sum, p) => sum + p, 0);
    const normalizedProbs = probabilities.map(p => p / total);

    html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0;">';
    html += '<div style="font-weight: bold; margin-bottom: 10px;">当前输入: "' + input + '"</div>';
    html += '<div style="font-weight: bold; margin-bottom: 10px;">预测下一个字符:</div>';

    vocab.forEach((char, index) => {
        const prob = normalizedProbs[index];
        const percentage = (prob * 100).toFixed(1);
        const barWidth = prob * 300;

        html += `
                <div style="display: flex; align-items: center; margin: 5px 0;">
                    <div style="width: 30px; text-align: center; font-weight: bold;">${char}</div>
                    <div style="flex: 1; margin: 0 10px;">
                        <div style="background: #e0e0e0; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: linear-gradient(90deg, #4CAF50, #8BC34A);
                                        height: 100%; width: ${barWidth}px;
                                        transition: width 0.5s ease;"></div>
                        </div>
                    </div>
                    <div style="width: 60px; text-align: right; font-family: monospace;">${percentage}%</div>
                </div>`;
    });

    html += '</div>';

    const topPrediction = vocab[0];
    html += `<div style="margin-top: 15px; padding: 15px; background: #e8f5e8; border-radius: 10px; border-left: 4px solid #4CAF50;">
                <strong>🎯 最终预测:</strong> "${topPrediction}" (概率: ${(normalizedProbs[0] * 100).toFixed(1)}%)
            </div>`;

    html += '<div style="margin-top: 15px; padding: 10px; background: #e1f5fe; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>通过softmax函数将最后的隐藏状态转换为词汇表上的概率分布，选择概率最高的词作为预测结果。';
    html += '</div>';

    // 添加专业知识详解
    html += `
        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 15px; color: #333;">
            <h4 style="color: #333; margin-bottom: 15px;">🎓 专业知识详解</h4>

            <div style="margin-bottom: 15px;">
                <strong>📌 演示目的：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    展示Transformer如何将最终的隐藏状态转换为词汇表上的概率分布，实现下一个词的预测。
                    这是语言模型的核心输出机制，决定了模型的生成能力。
                </p>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>🔬 体现的核心原理：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    • <strong>线性投影：</strong>将隐藏状态映射到词汇表大小的向量<br>
                    • <strong>Softmax归一化：</strong>将logits转换为概率分布，所有概率和为1<br>
                    • <strong>概率采样：</strong>根据概率分布选择下一个词（贪心或采样策略）
                </p>
            </div>

            <div>
                <strong>⚠️ 关键注意点：</strong>
                <p style="margin: 5px 0; line-height: 1.6;">
                    输出层的<strong>权重矩阵通常与输入嵌入层共享</strong>（权重绑定），这减少了参数量并提高了性能。
                    生成策略（贪心vs采样vs束搜索）会显著影响模型的输出质量和多样性。
                </p>
            </div>
        </div>
    `;

    result.innerHTML = html;
}

// 帮助功能
function toggleHelp() {
    const tooltip = document.getElementById('helpTooltip');
    tooltip.style.display = tooltip.style.display === 'block' ? 'none' : 'block';
}

// 可折叠功能
function toggleCollapsible(button) {
    button.classList.toggle('active');
    const content = button.nextElementSibling;
    const chevron = button.querySelector('.chevron');

    if (content.classList.contains('active')) {
        content.classList.remove('active');
        chevron.classList.remove('active');
    } else {
        content.classList.add('active');
        chevron.classList.add('active');
    }
}

// 更新进度条
function updateProgress() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / scrollHeight) * 100;
    document.getElementById('progressBar').style.width = progress + '%';
}

// 添加示例句子
const exampleSentences = [
    "我喜欢学习人工智能",
    "Transformer是强大的模型",
    "注意力机制很重要",
    "深度学习改变世界",
    "自然语言处理很有趣"
];

function getRandomExample() {
    return exampleSentences[Math.floor(Math.random() * exampleSentences.length)];
}

// 增强演示内容创建函数
function createDemoContent(type, demo) {
    const randomExample = getRandomExample();
    return `
                <h2>${demo.title}</h2>
                <p style="margin-bottom: 20px; color: #666;">${demo.description}</p>
                <div class="demo-area">
                    <div class="demo-controls">
                        <button class="control-btn" onclick="setExample('${randomExample}')">🎲 随机示例</button>
                        <button class="control-btn" onclick="setExample('我喜欢学习人工智能')">📝 默认示例</button>
                        <button class="control-btn" onclick="setExample('')">🗑️ 清空输入</button>
                    </div>
                    <input type="text" class="input-sentence" id="demoInput"
                           placeholder="请输入一句话，例如：我喜欢学习人工智能"
                           value="我喜欢学习人工智能">
                    <button class="process-btn" onclick="processDemo('${type}')">开始处理</button>
                    <button class="process-btn" onclick="clearDemo()" style="background: #dc3545;">清除结果</button>
                    <div class="result-area" id="demoResult">
                        <p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>
                    </div>
                </div>
            `;
}

// 设置示例文本
function setExample(text) {
    document.getElementById('demoInput').value = text;
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
        closeDemo();
    }
});

// 滚动事件监听
window.addEventListener('scroll', updateProgress);

// 点击外部关闭帮助提示
document.addEventListener('click', function (e) {
    const helpButton = document.querySelector('.floating-help');
    const helpTooltip = document.getElementById('helpTooltip');
    if (!helpButton.contains(e.target)) {
        helpTooltip.style.display = 'none';
    }
});

// Transformer流程演示相关变量
let flowRunning = false;
let currentFlowStep = 0;
let flowInterval = null;

// Transformer流程演示函数
function startTransformerFlow() {
    if (flowRunning) return;

    const input = document.getElementById('flowInput').value.trim();
    if (!input) {
        alert('请输入一句话！');
        return;
    }

    flowRunning = true;
    currentFlowStep = 0;

    // 重置所有步骤状态
    resetAllSteps();

    // 开始逐步演示
    processNextStep(input);
}

function resetTransformerFlow() {
    flowRunning = false;
    currentFlowStep = 0;

    if (flowInterval) {
        clearTimeout(flowInterval);
        flowInterval = null;
    }

    resetAllSteps();

    // 重置结果显示
    const resultContent = document.getElementById('resultContent');
    resultContent.innerHTML = '点击"开始处理"查看Transformer如何处理您的输入';
}

function resetAllSteps() {
    for (let i = 1; i <= 6; i++) {
        const step = document.getElementById(`step${i}`);
        const status = document.getElementById(`status${i}`);
        const content = document.getElementById(`content${i}`);

        step.classList.remove('active', 'completed');
        status.textContent = '等待开始';

        // 重置内容为初始状态
        switch (i) {
            case 1:
                content.innerHTML = '<p>将文字转换为数字向量...</p>';
                break;
            case 2:
                content.innerHTML = '<p>添加位置信息到词向量...</p>';
                break;
            case 3:
                content.innerHTML = '<p>计算词汇间的关注度...</p>';
                break;
            case 4:
                content.innerHTML = '<p>通过神经网络处理...</p>';
                break;
            case 5:
                content.innerHTML = '<p>标准化数据并添加残差连接...</p>';
                break;
            case 6:
                content.innerHTML = '<p>生成最终预测结果...</p>';
                break;
        }
    }
}

function processNextStep(input) {
    if (!flowRunning || currentFlowStep >= 6) {
        flowRunning = false;
        return;
    }

    currentFlowStep++;

    // 激活当前步骤
    const currentStep = document.getElementById(`step${currentFlowStep}`);
    const currentStatus = document.getElementById(`status${currentFlowStep}`);

    currentStep.classList.add('active');
    currentStatus.textContent = '处理中...';

    // 根据步骤类型生成内容
    setTimeout(() => {
        generateStepContent(currentFlowStep, input);

        // 标记当前步骤完成
        currentStep.classList.remove('active');
        currentStep.classList.add('completed');
        currentStatus.textContent = '已完成';

        // 如果是最后一步，显示最终结果
        if (currentFlowStep === 6) {
            showFinalResult(input);
            flowRunning = false;
        } else {
            // 继续下一步
            flowInterval = setTimeout(() => processNextStep(input), 1000);
        }
    }, 2000); // 每步处理2秒
}

function generateStepContent(stepNumber, input) {
    const content = document.getElementById(`content${stepNumber}`);
    const tokens = input.split('');

    switch (stepNumber) {
        case 1: // 输入嵌入
            content.innerHTML = showEmbeddingStep(tokens);
            break;
        case 2: // 位置编码
            content.innerHTML = showPositionalStep(tokens);
            break;
        case 3: // 多头注意力
            content.innerHTML = showAttentionStep(tokens);
            break;
        case 4: // 前馈网络
            content.innerHTML = showFeedForwardStep(tokens);
            break;
        case 5: // 归一化与残差
            content.innerHTML = showNormalizationStep(tokens);
            break;
        case 6: // 输出预测
            content.innerHTML = showOutputStep(tokens);
            break;
    }
}

function showEmbeddingStep(tokens) {
    let html = '<h4>🔍 词汇嵌入过程</h4>';
    html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin: 15px 0;">';

    tokens.forEach((token, index) => {
        const vector = Array.from({ length: 3 }, () => (Math.random() * 2 - 1).toFixed(2));
        html += `
            <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; text-align: center;">
                <div style="font-size: 1.2em; margin-bottom: 5px;">${token}</div>
                <div style="font-size: 0.8em; opacity: 0.8;">[${vector.join(', ')}]</div>
            </div>`;
    });

    html += '</div>';
    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 每个字符都被转换为512维向量（这里简化显示为3维）</p>';
    return html;
}

function showPositionalStep(tokens) {
    let html = '<h4>📍 位置编码过程</h4>';
    html += '<div style="margin: 15px 0;">';

    tokens.forEach((token, index) => {
        const pos = index + 1;
        const posEncoding = Math.sin(pos / 10000).toFixed(3);
        html += `
            <div style="display: flex; align-items: center; margin: 8px 0; background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px;">
                <div style="width: 40px; text-align: center; font-weight: bold;">${token}</div>
                <div style="flex: 1; margin: 0 10px; font-size: 0.9em;">
                    位置${pos} + 编码(${posEncoding})
                </div>
                <div style="width: 80px; height: 15px; background: linear-gradient(90deg,
                    hsl(${(pos * 30) % 360}, 70%, 70%),
                    hsl(${(pos * 30 + 60) % 360}, 70%, 70%));
                    border-radius: 8px;"></div>
            </div>`;
    });

    html += '</div>';
    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 位置信息已添加到每个词向量中</p>';
    return html;
}

function showAttentionStep(tokens) {
    let html = '<h4>🎯 多头注意力计算</h4>';
    html += '<div style="margin: 15px 0;">';

    // 创建注意力矩阵
    html += '<div style="display: grid; grid-template-columns: 50px repeat(' + tokens.length + ', 40px); gap: 2px; margin: 10px 0;">';

    // 表头
    html += '<div></div>';
    tokens.forEach(token => {
        html += `<div style="text-align: center; font-weight: bold; font-size: 0.8em;">${token}</div>`;
    });

    // 注意力矩阵
    for (let i = 0; i < tokens.length; i++) {
        html += `<div style="writing-mode: vertical-rl; text-align: center; font-weight: bold; font-size: 0.8em;">${tokens[i]}</div>`;
        for (let j = 0; j < tokens.length; j++) {
            const attention = Math.random();
            const intensity = Math.floor(attention * 255);
            const color = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
            html += `<div style="background: ${color}; color: ${intensity > 128 ? 'white' : 'black'};
                        width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;
                        border-radius: 4px; font-size: 0.7em; font-weight: bold;">
                        ${attention.toFixed(2)}
                    </div>`;
        }
    }

    html += '</div>';
    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 计算了每个词对其他词的注意力权重</p>';
    return html;
}

function showFeedForwardStep(tokens) {
    let html = '<h4>⚡ 前馈网络处理</h4>';
    html += '<div style="margin: 15px 0;">';

    tokens.forEach((token, index) => {
        html += `
            <div style="margin: 10px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的处理过程：</div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="padding: 8px 12px; background: #e3f2fd; color: #333; border-radius: 5px; font-size: 0.9em;">
                        输入层<br><small>[512维]</small>
                    </div>
                    <div style="font-size: 1.5em;">→</div>
                    <div style="padding: 8px 12px; background: #fff3e0; color: #333; border-radius: 5px; font-size: 0.9em;">
                        隐藏层<br><small>[2048维]</small>
                    </div>
                    <div style="font-size: 1.5em;">→</div>
                    <div style="padding: 8px 12px; background: #e8f5e8; color: #333; border-radius: 5px; font-size: 0.9em;">
                        输出层<br><small>[512维]</small>
                    </div>
                </div>
            </div>`;
    });

    html += '</div>';
    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 每个位置的特征都通过前馈网络进行了非线性变换</p>';
    return html;
}

function showNormalizationStep(tokens) {
    let html = '<h4>🔄 层归一化与残差连接</h4>';
    html += '<div style="margin: 15px 0;">';

    tokens.forEach((token, index) => {
        const beforeNorm = Array.from({ length: 4 }, () => (Math.random() * 10 - 5).toFixed(2));
        const afterNorm = beforeNorm.map(x => ((x - 0) / 2.5).toFixed(2));

        html += `
            <div style="margin: 10px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的归一化过程：</div>

                <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
                    <div>
                        <div style="font-size: 0.9em; opacity: 0.8; margin-bottom: 5px;">归一化前:</div>
                        <div style="font-family: monospace; background: rgba(255,255,255,0.2); padding: 8px; border-radius: 4px; font-size: 0.8em;">
                            [${beforeNorm.join(', ')}]
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <div style="background: #2196F3; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em;">
                            LayerNorm
                        </div>
                    </div>

                    <div>
                        <div style="font-size: 0.9em; opacity: 0.8; margin-bottom: 5px;">归一化后:</div>
                        <div style="font-family: monospace; background: rgba(255,255,255,0.2); padding: 8px; border-radius: 4px; font-size: 0.8em;">
                            [${afterNorm.join(', ')}]
                        </div>
                    </div>
                </div>
            </div>`;
    });

    html += '</div>';
    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 数据已标准化，残差连接保持了信息流动</p>';
    return html;
}

function showOutputStep(tokens) {
    const vocab = ['我', '你', '他', '喜', '欢', '学', '习', '人', '工', '智', '能', '的', '是', '在', '有'];

    let html = '<h4>📊 输出预测过程</h4>';
    html += '<div style="margin: 15px 0;">';

    // 生成概率分布
    const probabilities = vocab.map(() => Math.random()).sort((a, b) => b - a);
    const total = probabilities.reduce((sum, p) => sum + p, 0);
    const normalizedProbs = probabilities.map(p => p / total);

    html += '<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">';
    html += '<div style="font-weight: bold; margin-bottom: 10px;">预测下一个字符的概率分布：</div>';

    vocab.slice(0, 8).forEach((char, index) => {
        const prob = normalizedProbs[index];
        const percentage = (prob * 100).toFixed(1);
        const barWidth = prob * 200;

        html += `
            <div style="display: flex; align-items: center; margin: 5px 0;">
                <div style="width: 30px; text-align: center; font-weight: bold;">${char}</div>
                <div style="flex: 1; margin: 0 10px;">
                    <div style="background: rgba(255,255,255,0.3); height: 15px; border-radius: 8px; overflow: hidden;">
                        <div style="background: linear-gradient(90deg, #4CAF50, #8BC34A);
                                    height: 100%; width: ${barWidth}px;
                                    transition: width 0.5s ease;"></div>
                    </div>
                </div>
                <div style="width: 60px; text-align: right; font-family: monospace; font-size: 0.9em;">${percentage}%</div>
            </div>`;
    });

    html += '</div>';

    const topPrediction = vocab[0];
    html += `<div style="margin-top: 15px; padding: 15px; background: rgba(76, 175, 80, 0.3); border-radius: 10px; border-left: 4px solid #4CAF50;">
                <strong>🎯 最终预测:</strong> "${topPrediction}" (概率: ${(normalizedProbs[0] * 100).toFixed(1)}%)
            </div>`;

    html += '<p style="font-size: 0.9em; opacity: 0.9;">✅ 通过softmax函数生成了词汇表上的概率分布</p>';
    return html;
}

function showFinalResult(input) {
    const resultContent = document.getElementById('resultContent');
    const vocab = ['我', '你', '他', '喜', '欢', '学', '习', '人', '工', '智', '能', '的', '是', '在', '有'];
    const prediction = vocab[Math.floor(Math.random() * vocab.length)];

    resultContent.innerHTML = `
        <div style="background: rgba(76, 175, 80, 0.2); border-radius: 15px; padding: 20px; margin: 15px 0;">
            <h4 style="color: #4CAF50; margin-bottom: 15px;">🎉 处理完成！</h4>
            <div style="font-size: 1.1em; margin-bottom: 10px;">
                <strong>输入：</strong>"${input}"
            </div>
            <div style="font-size: 1.1em; margin-bottom: 15px;">
                <strong>预测下一个字：</strong>"${prediction}"
            </div>
            <div style="font-size: 0.95em; opacity: 0.9; line-height: 1.5;">
                Transformer已经完成了对您输入的完整处理流程：<br>
                文字→向量→位置→注意力→前馈→归一化→预测输出
            </div>
        </div>
    `;
}

// 添加页面加载动画
document.addEventListener('DOMContentLoaded', function () {
    const modules = document.querySelectorAll('.module');
    modules.forEach((module, index) => {
        setTimeout(() => {
            module.style.opacity = '1';
            module.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // 初始化进度条
    updateProgress();
});

// ==================== 核心代码弹窗功能 ====================

function showCoreCode(moduleType) {
    const modal = document.getElementById('codeModal');
    const title = document.getElementById('codeModalTitle');
    const description = document.getElementById('codeModalDescription');
    const content = document.getElementById('codeModalContent');

    // 检查TransformerCoreCode是否已加载
    if (typeof TransformerCoreCode === 'undefined') {
        alert('核心代码库正在加载中，请稍后再试...');
        return;
    }

    const codeData = TransformerCoreCode[moduleType];

    if (!codeData) {
        alert('未找到该模块的核心代码');
        return;
    }

    // 设置弹窗内容
    title.textContent = codeData.title;
    description.textContent = codeData.description;
    content.textContent = codeData.code;

    // 显示弹窗
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden'; // 防止背景滚动

    // 添加动画效果
    setTimeout(() => {
        const modalContent = modal.querySelector('.code-modal-content');
        modalContent.style.transform = 'scale(1)';
        modalContent.style.opacity = '1';
    }, 10);
}

function closeCoreCodeModal() {
    const modal = document.getElementById('codeModal');
    const modalContent = modal.querySelector('.code-modal-content');

    // 添加关闭动画
    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';

    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // 恢复背景滚动
    }, 300);
}

function copyCodeToClipboard() {
    const codeContent = document.getElementById('codeModalContent');
    const textArea = document.createElement('textarea');
    textArea.value = codeContent.textContent;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');

        // 更新按钮文本提供反馈
        const copyBtn = document.querySelector('.copy-code-btn');
        const originalText = copyBtn.textContent;
        copyBtn.textContent = '✅ 已复制!';
        copyBtn.style.background = 'linear-gradient(45deg, #48bb78, #38a169)';

        setTimeout(() => {
            copyBtn.textContent = originalText;
            copyBtn.style.background = 'linear-gradient(45deg, #48bb78, #38a169)';
        }, 2000);

    } catch (err) {
        // 尝试使用现代API
        if (navigator.clipboard) {
            navigator.clipboard.writeText(codeContent.textContent).then(() => {
                const copyBtn = document.querySelector('.copy-code-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ 已复制!';

                setTimeout(() => {
                    copyBtn.textContent = originalText;
                }, 2000);
            }).catch(() => {
                alert('复制失败，请手动选择代码进行复制');
            });
        } else {
            alert('复制失败，请手动选择代码进行复制');
        }
    }

    document.body.removeChild(textArea);
}

// 点击弹窗外部关闭弹窗
window.addEventListener('click', function (event) {
    const modal = document.getElementById('codeModal');
    if (event.target === modal) {
        closeCoreCodeModal();
    }
});

// ESC键关闭弹窗
document.addEventListener('keydown', function (event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('codeModal');
        if (modal && modal.style.display === 'block') {
            closeCoreCodeModal();
        }
    }
});
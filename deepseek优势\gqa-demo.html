<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组查询注意力机制（GQA）演示</title>
    <link rel="stylesheet" href="gqa-style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>分组查询注意力机制（GQA）演示</h1>
            <p class="subtitle">DeepSeek的计算效率优化技术</p>
        </header>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="mode-switch">
                    <button id="mhaMode" class="btn-mode active">🔵 传统MHA</button>
                    <button id="gqaMode" class="btn-mode">🟢 GQA优化</button>
                </div>
                
                <div class="demo-controls">
                    <button id="startDemo" class="btn-primary">开始演示</button>
                    <button id="resetDemo" class="btn-secondary">重置</button>
                    <button id="nextStep" class="btn-step" disabled>下一步</button>
                </div>
                
                <div class="progress-area">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span class="step-counter" id="stepCounter">步骤 0/4</span>
                </div>
            </div>

            <!-- 主要演示区域 -->
            <div class="demo-layout">
                <!-- 左侧：架构对比 -->
                <div class="architecture-panel">
                    <h3 id="architectureTitle">传统多头注意力（MHA）</h3>
                    <div class="architecture-container" id="architectureContainer">
                        <!-- 动态生成的架构图 -->
                    </div>
                    
                    <!-- 性能指标 -->
                    <div class="performance-metrics" id="performanceMetrics">
                        <div class="metric-item">
                            <span class="metric-label">计算复杂度:</span>
                            <span class="metric-value" id="complexityValue">O(n²d)</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">内存占用:</span>
                            <span class="metric-value" id="memoryValue">100%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">推理速度:</span>
                            <span class="metric-value" id="speedValue">1x</span>
                        </div>
                    </div>
                </div>

                <!-- 中间：步骤演示 -->
                <div class="step-demo-panel">
                    <h3 id="stepTitle">选择模式开始演示</h3>
                    <div class="step-description" id="stepDescription">
                        <p>点击上方的模式切换按钮，然后点击"开始演示"来了解不同注意力机制的工作原理。</p>
                    </div>
                    <div class="step-visualization" id="stepVisualization">
                        <!-- 动态可视化内容 -->
                    </div>
                </div>

                <!-- 右侧：知识点详解 -->
                <div class="knowledge-panel">
                    <h3>💡 知识点详解</h3>
                    <div class="knowledge-content" id="knowledgeContent">
                        <div class="welcome-knowledge">
                            <h4>🎯 学习目标</h4>
                            <ul>
                                <li>理解传统多头注意力机制的计算过程</li>
                                <li>掌握分组查询注意力（GQA）的优化原理</li>
                                <li>对比两种机制的性能差异</li>
                                <li>了解GQA在实际应用中的优势</li>
                            </ul>
                            
                            <h4>📚 核心概念</h4>
                            <div class="concept-box">
                                <p><strong>分组查询注意力（GQA）</strong>是DeepSeek等先进模型采用的优化技术，通过将相似的查询向量分组处理，显著降低计算复杂度。</p>
                            </div>
                            
                            <h4>🔗 相关链接</h4>
                            <p><a href="index.html" style="color: #4CAF50; text-decoration: none;">← 返回Transformer演示</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 类比说明区域 -->
        <div class="analogy-section">
            <h3>📘 生活化类比</h3>
            <div class="analogy-content" id="analogyContent">
                <div class="analogy-card">
                    <h4>🍽️ 餐厅点餐类比</h4>
                    <div class="analogy-comparison">
                        <div class="analogy-item">
                            <h5>传统MHA方式</h5>
                            <p>10个朋友各自单独点餐，服务员需要逐个处理每份订单，即使有些人点的是相同菜品。</p>
                            <div class="analogy-visual">
                                <span class="customer">👤</span>
                                <span class="arrow">→</span>
                                <span class="waiter">👨‍🍳</span>
                                <span class="customer">👤</span>
                                <span class="arrow">→</span>
                                <span class="waiter">👨‍🍳</span>
                                <span class="customer">👤</span>
                                <span class="arrow">→</span>
                                <span class="waiter">👨‍🍳</span>
                            </div>
                        </div>
                        <div class="analogy-item">
                            <h5>GQA优化方式</h5>
                            <p>将点相同菜品的朋友分组，服务员可以批量处理相同订单，大大提高效率。</p>
                            <div class="analogy-visual">
                                <span class="group">👥</span>
                                <span class="arrow">→</span>
                                <span class="waiter">👨‍🍳</span>
                                <span class="group">👥</span>
                                <span class="arrow">→</span>
                                <span class="waiter">👨‍🍳</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="gqa-script.js"></script>
</body>
</html>

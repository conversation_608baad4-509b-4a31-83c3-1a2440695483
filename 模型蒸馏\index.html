<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型蒸馏交互式学习平台</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- 背景动画 -->
    <div class="background-animation">
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 标题区域 -->
        <header class="header">
            <h1 class="main-title">
                <span class="title-icon">🧠</span>
                模型蒸馏交互式学习平台
                <span class="title-subtitle">Model Distillation Interactive Learning</span>
            </h1>
            <p class="header-description">
                从大模型到小模型的知识传递之旅 - 让AI变得更轻量、更高效
            </p>
        </header>

        <!-- 导航标签 -->
        <nav class="demo-tabs">
            <button class="tab-btn active" data-demo="overview">
                <span class="tab-icon">📚</span>
                蒸馏概览
            </button>
            <button class="tab-btn" data-demo="targets">
                <span class="tab-icon">🎯</span>
                软硬目标
            </button>
            <button class="tab-btn" data-demo="temperature">
                <span class="tab-icon">🌡️</span>
                温度参数
            </button>
            <button class="tab-btn" data-demo="process">
                <span class="tab-icon">⚗️</span>
                蒸馏过程
            </button>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 蒸馏概览演示 -->
            <section class="demo-section active" id="overview-section">
                <div class="content-layout">
                    <div class="demo-container">
                        <h3 class="demo-title">🧠 教师模型 vs 学生模型对比演示</h3>

                        <div class="model-comparison">
                            <!-- 教师模型 -->
                            <div class="model-card teacher-model">
                                <div class="model-header">
                                    <h4>👨‍🏫 教师模型 (Teacher Model)</h4>
                                    <div class="model-size">参数量: 175B</div>
                                </div>
                                <div class="model-brain">
                                    <div class="brain-layers">
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                    </div>
                                </div>
                                <div class="model-stats">
                                    <div class="stat">
                                        <span class="stat-label">准确率</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill teacher-accuracy" style="width: 95%"></div>
                                        </div>
                                        <span class="stat-value">95%</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">速度</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill teacher-speed" style="width: 20%"></div>
                                        </div>
                                        <span class="stat-value">慢</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">资源消耗</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill teacher-resource" style="width: 90%"></div>
                                        </div>
                                        <span class="stat-value">高</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 蒸馏箭头 -->
                            <div class="distillation-arrow">
                                <div class="arrow-body">
                                    <span class="arrow-text">知识蒸馏</span>
                                    <div class="knowledge-particles">
                                        <div class="particle"></div>
                                        <div class="particle"></div>
                                        <div class="particle"></div>
                                    </div>
                                </div>
                                <div class="arrow-head">→</div>
                            </div>

                            <!-- 学生模型 -->
                            <div class="model-card student-model">
                                <div class="model-header">
                                    <h4>👨‍🎓 学生模型 (Student Model)</h4>
                                    <div class="model-size">参数量: 7B</div>
                                </div>
                                <div class="model-brain">
                                    <div class="brain-layers">
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                        <div class="layer"></div>
                                    </div>
                                </div>
                                <div class="model-stats">
                                    <div class="stat">
                                        <span class="stat-label">准确率</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill student-accuracy" style="width: 88%"></div>
                                        </div>
                                        <span class="stat-value">88%</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">速度</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill student-speed" style="width: 85%"></div>
                                        </div>
                                        <span class="stat-value">快</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">资源消耗</span>
                                        <div class="stat-bar">
                                            <div class="stat-fill student-resource" style="width: 25%"></div>
                                        </div>
                                        <span class="stat-value">低</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="demo-controls">
                            <button class="control-btn" onclick="startDistillationDemo()">
                                <span class="btn-icon">▶️</span>
                                开始蒸馏演示
                            </button>
                            <button class="control-btn" onclick="resetDistillationDemo()">
                                <span class="btn-icon">🔄</span>
                                重置演示
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('overview')">
                                <span class="btn-icon">💻</span>
                                实现该功能的核心JS代码
                            </button>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 模型蒸馏基础知识</h3>

                        <div class="knowledge-section">
                            <h4>🎯 什么是模型蒸馏？</h4>
                            <p>模型蒸馏就像是"知识传承"的过程。想象一位经验丰富的老教授（教师模型）要把自己的知识传授给年轻的助教（学生模型）。老教授知识渊博但讲课很慢，而助教虽然经验不足，但反应敏捷、效率很高。
                            </p>

                            <div class="analogy-box">
                                <h5>🏠 生活类比</h5>
                                <p><strong>教师模型</strong> = 资深大厨（技艺精湛，但动作慢，需要很多厨具）</p>
                                <p><strong>学生模型</strong> = 快餐店厨师（学会了大厨的核心技巧，动作快，设备简单）</p>
                                <p><strong>蒸馏过程</strong> = 大厨教快餐店厨师如何在保持味道的同时提高效率</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🔍 为什么需要模型蒸馏？</h4>
                            <ul class="feature-list">
                                <li><strong>资源限制：</strong>大模型需要大量GPU内存和计算资源</li>
                                <li><strong>部署成本：</strong>在移动设备或边缘设备上部署大模型成本高昂</li>
                                <li><strong>推理速度：</strong>小模型推理速度更快，用户体验更好</li>
                                <li><strong>能耗考虑：</strong>小模型功耗更低，更环保</li>
                            </ul>
                        </div>

                        <div class="knowledge-section">
                            <h4>⚡ 蒸馏的核心优势</h4>
                            <div class="advantage-grid">
                                <div class="advantage-item">
                                    <span class="advantage-icon">🚀</span>
                                    <h5>速度提升</h5>
                                    <p>推理速度提升5-10倍</p>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">💾</span>
                                    <h5>内存节省</h5>
                                    <p>模型大小减少80-95%</p>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">🎯</span>
                                    <h5>性能保持</h5>
                                    <p>保持85-95%的原始性能</p>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">🌱</span>
                                    <h5>绿色AI</h5>
                                    <p>大幅降低能耗和碳排放</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 软硬目标对比演示 -->
            <section class="demo-section" id="targets-section">
                <div class="content-layout">
                    <div class="demo-container">
                        <h3 class="demo-title">🎯 软目标 vs 硬目标对比演示</h3>

                        <div class="targets-comparison">
                            <!-- 场景描述 -->
                            <div class="scenario-description">
                                <div class="scenario-card">
                                    <h4>📝 学习场景：多选题答题</h4>
                                    <div class="question-display">
                                        <p class="question-text">
                                            <strong>问题：</strong>下列哪个选项最能描述"机器学习"的核心概念？
                                        </p>
                                        <div class="options-grid">
                                            <div class="option-item" data-option="A">
                                                <span class="option-label">A</span>
                                                <span class="option-text">让计算机通过数据自动学习规律</span>
                                            </div>
                                            <div class="option-item" data-option="B">
                                                <span class="option-label">B</span>
                                                <span class="option-text">编写复杂的程序代码</span>
                                            </div>
                                            <div class="option-item correct" data-option="C">
                                                <span class="option-label">C</span>
                                                <span class="option-text">使用算法从数据中发现模式并做出预测</span>
                                                <span class="correct-mark">✅</span>
                                            </div>
                                            <div class="option-item" data-option="D">
                                                <span class="option-label">D</span>
                                                <span class="option-text">人工设定所有决策规则</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 对比展示 -->
                            <div class="comparison-display">
                                <!-- 硬目标 -->
                                <div class="target-card hard-target">
                                    <h4>
                                        <span class="target-icon">🔲</span>
                                        硬目标标签 (Hard Target)
                                    </h4>
                                    <div class="target-explanation">
                                        <p>传统的one-hot编码，只标记正确答案</p>
                                    </div>
                                    <div class="target-values">
                                        <div class="value-row">
                                            <span class="option-label">A</span>
                                            <div class="value-bar hard">
                                                <div class="bar-fill" style="width: 0%"></div>
                                            </div>
                                            <span class="value-number">0</span>
                                        </div>
                                        <div class="value-row">
                                            <span class="option-label">B</span>
                                            <div class="value-bar hard">
                                                <div class="bar-fill" style="width: 0%"></div>
                                            </div>
                                            <span class="value-number">0</span>
                                        </div>
                                        <div class="value-row correct">
                                            <span class="option-label">C</span>
                                            <div class="value-bar hard">
                                                <div class="bar-fill" style="width: 100%"></div>
                                            </div>
                                            <span class="value-number">1</span>
                                            <span class="correct-indicator">✅</span>
                                        </div>
                                        <div class="value-row">
                                            <span class="option-label">D</span>
                                            <div class="value-bar hard">
                                                <div class="bar-fill" style="width: 0%"></div>
                                            </div>
                                            <span class="value-number">0</span>
                                        </div>
                                    </div>
                                    <div class="target-summary">
                                        <p class="summary-text">
                                            <span class="summary-icon">📊</span>
                                            信息量：<strong>有限</strong> - 只知道正确答案
                                        </p>
                                    </div>
                                </div>

                                <!-- 软目标 -->
                                <div class="target-card soft-target">
                                    <h4>
                                        <span class="target-icon">🌈</span>
                                        软目标概率 (Soft Target)
                                    </h4>
                                    <div class="target-explanation">
                                        <p>教师模型的概率分布，包含丰富的知识信息</p>
                                    </div>
                                    <div class="target-values">
                                        <div class="value-row">
                                            <span class="option-label">A</span>
                                            <div class="value-bar soft">
                                                <div class="bar-fill" style="width: 20%"></div>
                                            </div>
                                            <span class="value-number">0.20</span>
                                            <span class="insight">有一定相关性</span>
                                        </div>
                                        <div class="value-row">
                                            <span class="option-label">B</span>
                                            <div class="value-bar soft">
                                                <div class="bar-fill" style="width: 10%"></div>
                                            </div>
                                            <span class="value-number">0.10</span>
                                            <span class="insight">基本无关</span>
                                        </div>
                                        <div class="value-row correct">
                                            <span class="option-label">C</span>
                                            <div class="value-bar soft">
                                                <div class="bar-fill" style="width: 60%"></div>
                                            </div>
                                            <span class="value-number">0.60</span>
                                            <span class="correct-indicator">✅</span>
                                            <span class="insight">最佳答案</span>
                                        </div>
                                        <div class="value-row">
                                            <span class="option-label">D</span>
                                            <div class="value-bar soft">
                                                <div class="bar-fill" style="width: 10%"></div>
                                            </div>
                                            <span class="value-number">0.10</span>
                                            <span class="insight">有干扰性</span>
                                        </div>
                                    </div>
                                    <div class="target-summary">
                                        <p class="summary-text">
                                            <span class="summary-icon">🧠</span>
                                            信息量：<strong>丰富</strong> - 包含判断逻辑和相似度
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 学习效果对比 -->
                            <div class="learning-effects">
                                <h4>📈 学习效果对比</h4>
                                <div class="effects-grid">
                                    <div class="effect-card">
                                        <h5>🔲 硬目标学习</h5>
                                        <ul class="effect-list">
                                            <li>只知道正确答案是什么</li>
                                            <li>无法理解选项间的相似度</li>
                                            <li>容易过拟合到训练数据</li>
                                            <li>泛化能力相对有限</li>
                                        </ul>
                                        <div class="effect-score">
                                            <span class="score-label">学习深度：</span>
                                            <div class="score-bar">
                                                <div class="score-fill basic" style="width: 40%"></div>
                                            </div>
                                            <span class="score-text">基础</span>
                                        </div>
                                    </div>
                                    <div class="effect-card">
                                        <h5>🌈 软目标学习</h5>
                                        <ul class="effect-list">
                                            <li>理解教师模型的判断逻辑</li>
                                            <li>学会区分相似但不正确的选项</li>
                                            <li>获得更细腻的语义理解</li>
                                            <li>显著提升泛化能力</li>
                                        </ul>
                                        <div class="effect-score">
                                            <span class="score-label">学习深度：</span>
                                            <div class="score-bar">
                                                <div class="score-fill advanced" style="width: 85%"></div>
                                            </div>
                                            <span class="score-text">深入</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 演示控制按钮 -->
                        <div class="demo-controls">
                            <button class="control-btn" onclick="replayTargetsAnimation()">
                                <span class="btn-icon">🎬</span>
                                重新播放动画
                            </button>
                            <button class="control-btn" onclick="toggleTargetsComparison()">
                                <span class="btn-icon">🔄</span>
                                切换对比视图
                            </button>
                        </div>
                    </div>

                    <!-- 知识点详解 -->
                    <div class="knowledge-container">
                        <h4>💡 核心知识点</h4>
                        <div class="knowledge-grid">
                            <div class="knowledge-card">
                                <h5>🎯 什么是软目标？</h5>
                                <p>软目标是教师模型输出的概率分布，包含了模型对所有选项的"认知程度"。它不仅告诉我们正确答案，还揭示了模型的判断逻辑。</p>
                            </div>
                            <div class="knowledge-card">
                                <h5>🔍 为什么软目标更有效？</h5>
                                <p>软目标包含了丰富的"暗知识"(Dark Knowledge)，帮助学生模型理解：哪些错误选项与正确答案相似，哪些完全无关，从而学会更细腻的判断。</p>
                            </div>
                            <div class="knowledge-card">
                                <h5>📊 实际应用价值</h5>
                                <p>在实际应用中，软目标训练的模型在面对模糊或边界情况时表现更好，因为它们学会了"不确定性"的处理方式。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 温度参数演示 -->
            <section class="demo-section" id="temperature-section">
                <div class="content-layout">
                    <div class="demo-container">
                        <h3 class="demo-title">🌡️ 温度参数与软目标演示</h3>

                        <div class="temperature-demo">
                            <div class="temperature-control">
                                <label for="temperature-slider">温度参数 T:</label>
                                <input type="range" id="temperature-slider" min="0.1" max="10" step="0.1" value="1">
                                <span id="temperature-value">1.0</span>
                            </div>

                            <div class="prediction-comparison">
                                <div class="prediction-card">
                                    <h4>🎯 原始输出 (Logits)</h4>
                                    <div class="scenario-info">
                                        <div class="scenario-header">
                                            <span class="scenario-icon">🐱</span>
                                            <span class="scenario-title">动物识别场景</span>
                                        </div>
                                        <div class="question-box">
                                            <h5>🤔 模型要回答的问题：</h5>
                                            <p class="question-text">"这张图片中的动物是什么？"</p>
                                            <div class="input-hint">
                                                <span class="hint-icon">🖼️</span>
                                                <span class="hint-text">输入：一张包含小动物的图片</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="logits-display" id="logits-display">
                                        <div class="logit-bar">
                                            <span class="label">🐱 猫</span>
                                            <div class="bar">
                                                <div class="fill" style="width: 80%"></div>
                                            </div>
                                            <span class="value">4.0</span>
                                        </div>
                                        <div class="logit-bar">
                                            <span class="label">🐶 狗</span>
                                            <div class="bar">
                                                <div class="fill" style="width: 60%"></div>
                                            </div>
                                            <span class="value">3.0</span>
                                        </div>
                                        <div class="logit-bar">
                                            <span class="label">🐦 鸟</span>
                                            <div class="bar">
                                                <div class="fill" style="width: 40%"></div>
                                            </div>
                                            <span class="value">2.0</span>
                                        </div>
                                        <div class="logit-bar">
                                            <span class="label">🐠 鱼</span>
                                            <div class="bar">
                                                <div class="fill" style="width: 20%"></div>
                                            </div>
                                            <span class="value">1.0</span>
                                        </div>
                                    </div>
                                    <div class="logits-explanation">
                                        <p class="explanation-text">
                                            <span class="explanation-icon">💡</span>
                                            Logits值越大，模型认为该类别的可能性越高
                                        </p>
                                    </div>
                                </div>

                                <div class="prediction-card">
                                    <h4>🌡️ 温度调节后的概率</h4>
                                    <div class="probability-display" id="probability-display">
                                        <!-- 动态生成 -->
                                    </div>
                                </div>
                            </div>

                            <div class="temperature-explanation">
                                <div class="explanation-card low-temp">
                                    <h5>❄️ 低温度 (T ≈ 1)</h5>
                                    <p>模型非常确定："这就是猫！"</p>
                                    <p>概率分布很尖锐，信息量较少</p>
                                </div>
                                <div class="explanation-card high-temp">
                                    <h5>🔥 高温度 (T > 3)</h5>
                                    <p>模型说："主要是猫，但也可能是狗，鸟也有点像..."</p>
                                    <p>概率分布平缓，包含更多信息</p>
                                </div>
                            </div>
                        </div>

                        <div class="demo-controls">
                            <button class="control-btn" onclick="animateTemperatureEffect()">
                                <span class="btn-icon">🎬</span>
                                演示温度效果
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('temperature')">
                                <span class="btn-icon">💻</span>
                                实现该功能的核心JS代码
                            </button>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 温度参数详解</h3>

                        <div class="knowledge-section">
                            <h4>🌡️ 温度参数的作用</h4>
                            <p>温度参数控制模型输出的"确定性"程度。就像调节水的温度一样：</p>

                            <div class="temperature-analogy">
                                <div class="temp-state">
                                    <span class="temp-icon">🧊</span>
                                    <h5>低温 (T=0.1-1)</h5>
                                    <p>像冰一样"固化"的决策</p>
                                    <p>模型非常确定，只给出最可能的答案</p>
                                </div>
                                <div class="temp-state">
                                    <span class="temp-icon">💧</span>
                                    <h5>中温 (T=1-3)</h5>
                                    <p>像水一样"流动"的思考</p>
                                    <p>平衡确定性和多样性</p>
                                </div>
                                <div class="temp-state">
                                    <span class="temp-icon">💨</span>
                                    <h5>高温 (T>3)</h5>
                                    <p>像蒸汽一样"发散"的想法</p>
                                    <p>提供更多可能性，但确定性降低</p>
                                </div>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>📐 数学公式</h4>
                            <div class="formula-box">
                                <p><strong>Softmax with Temperature:</strong></p>
                                <p class="formula">P(i) = exp(z_i/T) / Σ exp(z_j/T)</p>
                                <p class="formula-desc">其中 z_i 是第i类的logit值，T是温度参数</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎯 实际应用场景</h4>
                            <ul class="application-list">
                                <li><strong>创意写作：</strong>高温度产生更有创意的文本</li>
                                <li><strong>精确分类：</strong>低温度确保准确的分类结果</li>
                                <li><strong>知识蒸馏：</strong>中等温度平衡信息量和准确性</li>
                                <li><strong>对话系统：</strong>动态调节温度适应不同场景</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 蒸馏过程演示 -->
            <section class="demo-section" id="process-section">
                <div class="content-layout">
                    <div class="demo-container">
                        <h3 class="demo-title">⚗️ 完整蒸馏过程演示</h3>

                        <div class="distillation-process">
                            <div class="process-step" id="step-1">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h4>📊 数据准备</h4>
                                    <div class="data-flow">
                                        <div class="data-item">训练数据</div>
                                        <div class="data-item">验证数据</div>
                                        <div class="data-item">测试数据</div>
                                    </div>
                                </div>
                            </div>

                            <div class="process-step" id="step-2">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h4>🧠 教师模型推理</h4>
                                    <div class="teacher-inference">
                                        <div class="input-data">输入: "这是一只可爱的小猫"</div>
                                        <div class="teacher-output">
                                            <div class="soft-target">软目标 (T=3)</div>
                                            <div class="hard-target">硬目标 (T=1)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="process-step" id="step-3">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h4>👨‍🎓 学生模型训练</h4>
                                    <div class="student-training">
                                        <div class="loss-components">
                                            <div class="loss-item">
                                                <span class="loss-label">蒸馏损失</span>
                                                <div class="loss-bar">
                                                    <div class="loss-fill distillation" style="width: 60%"></div>
                                                </div>
                                            </div>
                                            <div class="loss-item">
                                                <span class="loss-label">任务损失</span>
                                                <div class="loss-bar">
                                                    <div class="loss-fill task" style="width: 40%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="process-step" id="step-4">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h4>📈 性能评估</h4>
                                    <div class="evaluation-metrics">
                                        <div class="metric">
                                            <span class="metric-name">准确率</span>
                                            <span class="metric-value">88.5%</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-name">推理速度</span>
                                            <span class="metric-value">5x 提升</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-name">模型大小</span>
                                            <span class="metric-value">90% 减少</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="demo-controls">
                            <button class="control-btn" onclick="startProcessDemo()">
                                <span class="btn-icon">▶️</span>
                                开始过程演示
                            </button>
                            <button class="control-btn" onclick="resetProcessDemo()">
                                <span class="btn-icon">🔄</span>
                                重置过程
                            </button>
                            <button class="control-btn core-code-btn" onclick="showCoreCode('process')">
                                <span class="btn-icon">💻</span>
                                实现该功能的核心JS代码
                            </button>
                        </div>
                    </div>

                    <div class="knowledge-panel">
                        <h3 class="knowledge-title">📚 蒸馏过程详解</h3>

                        <div class="knowledge-section">
                            <h4>⚗️ 蒸馏的四个关键步骤</h4>
                            <ol class="process-list">
                                <li><strong>数据准备：</strong>收集高质量的训练数据，确保覆盖各种场景</li>
                                <li><strong>教师推理：</strong>使用大模型生成软目标和硬目标</li>
                                <li><strong>学生训练：</strong>小模型同时学习软目标和硬目标</li>
                                <li><strong>性能评估：</strong>验证蒸馏效果，调整超参数</li>
                            </ol>
                        </div>

                        <div class="knowledge-section">
                            <h4>🎯 损失函数设计</h4>
                            <div class="formula-box">
                                <p><strong>总损失 = α × 蒸馏损失 + β × 任务损失</strong></p>
                                <p class="formula">L_total = α × L_distill + β × L_task</p>
                                <p class="formula-desc">α和β是平衡两种损失的权重参数</p>
                            </div>
                        </div>

                        <div class="knowledge-section">
                            <h4>💡 蒸馏技巧与最佳实践</h4>
                            <ul class="tips-list">
                                <li><strong>温度选择：</strong>通常选择T=3-5，平衡信息量和训练稳定性</li>
                                <li><strong>损失权重：</strong>蒸馏损失权重通常比任务损失权重大</li>
                                <li><strong>数据增强：</strong>使用数据增强提高蒸馏效果</li>
                                <li><strong>渐进蒸馏：</strong>从简单任务开始，逐步增加复杂度</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>🧠 模型蒸馏交互式学习平台 | 让AI变得更轻量、更高效</p>
            <p>💡 通过可视化演示深入理解知识蒸馏的核心原理</p>
        </footer>
    </div>

    <script src="code.js"></script>
    <script src="script.js"></script>
</body>

</html>
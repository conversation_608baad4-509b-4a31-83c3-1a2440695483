/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.2em;
    color: #718096;
    font-weight: 300;
}

/* 知识介绍区域 */
.knowledge-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.knowledge-section h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.8em;
}

.knowledge-content p {
    margin-bottom: 15px;
    font-size: 1.1em;
    line-height: 1.8;
}

.knowledge-content ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.knowledge-content li {
    margin-bottom: 8px;
    font-size: 1.05em;
}

/* 步骤指导 */
.steps-section {
    margin-bottom: 30px;
}

.steps-section h2 {
    color: white;
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.step-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.step-card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.step-card p {
    color: #718096;
    line-height: 1.6;
}

/* 演示区域 */
.demo-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
    color: #2d3748;
    margin-bottom: 25px;
    font-size: 1.8em;
}

/* 输入区域 */
.input-area {
    margin-bottom: 30px;
    text-align: center;
}

.input-area label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #4a5568;
    font-size: 1.1em;
}

#sentence-input {
    width: 60%;
    padding: 15px;
    font-size: 1.1em;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    margin-right: 15px;
    transition: border-color 0.3s ease;
}

#sentence-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#analyze-btn {
    padding: 15px 30px;
    font-size: 1.1em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-weight: bold;
}

#analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 当前步骤显示 */
.current-step {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: center;
}

.current-step h3 {
    font-size: 1.4em;
    margin-bottom: 10px;
}

.current-step p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 词语容器 */
.words-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    min-height: 80px;
}

.word-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 1.2em;
    font-weight: bold;
    color: #8b4513;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.word-card:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.word-card.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.1);
}

/* 矩阵容器 */
.matrices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.matrix-section h4 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3em;
    text-align: center;
}

.matrix-explanation {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.matrix-explanation p {
    margin-bottom: 8px;
    font-size: 0.95em;
    color: #4a5568;
}

.matrix {
    display: grid;
    gap: 3px;
    background: #e2e8f0;
    padding: 10px;
    border-radius: 8px;
    justify-items: center;
}

.matrix-cell {
    background: white;
    padding: 8px;
    border-radius: 4px;
    font-size: 0.9em;
    text-align: center;
    min-width: 50px;
    transition: all 0.3s ease;
}

/* 注意力分数 */
.attention-scores {
    margin-bottom: 30px;
}

.attention-scores h4 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.4em;
    text-align: center;
}

.attention-explanation {
    background: #f0fff4;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #48bb78;
}

.attention-explanation p {
    margin-bottom: 10px;
    color: #2d3748;
}

.color-high {
    background: #2d3748;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
}

.color-low {
    background: #e2e8f0;
    color: #4a5568;
    padding: 2px 6px;
    border-radius: 3px;
}

.attention-matrix {
    display: grid;
    gap: 2px;
    background: #e2e8f0;
    padding: 15px;
    border-radius: 10px;
    justify-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.attention-cell {
    background: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 0.9em;
    text-align: center;
    min-width: 60px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.attention-cell:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 控制按钮 */
.controls {
    text-align: center;
    margin-top: 30px;
}

.controls button {
    padding: 12px 25px;
    margin: 0 10px;
    font-size: 1em;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

#prev-step {
    background: #e2e8f0;
    color: #4a5568;
}

#next-step {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#reset-demo {
    background: #fed7d7;
    color: #c53030;
}

.controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 示例注意力矩阵区域 */
.example-attention-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.example-attention-section h2 {
    color: #2d3748;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-align: center;
}

.example-content {
    max-width: 1000px;
    margin: 0 auto;
}

.example-explanation {
    background: #f0f8ff;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    border-left: 5px solid #4299e1;
}

.example-explanation h3 {
    color: #2b6cb0;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.key-insights {
    margin-top: 20px;
}

.key-insights p {
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 10px;
}

.key-insights ul {
    margin-left: 20px;
}

.key-insights li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.highlight-deep {
    background: #2d3748;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: bold;
}

.highlight-light {
    background: #e2e8f0;
    color: #4a5568;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: bold;
}

.example-matrix-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: start;
    margin-bottom: 30px;
}

.example-attention-matrix {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 2px;
    background: #e2e8f0;
    padding: 15px;
    border-radius: 12px;
    max-width: 650px;
    margin: 0 auto;
}

.example-cell {
    background: white;
    padding: 8px 4px;
    border-radius: 4px;
    font-size: 0.85em;
    text-align: center;
    min-height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 500;
}

.example-cell:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.example-cell.header {
    background: #4a5568;
    color: white;
    font-weight: bold;
    font-size: 0.8em;
}

.matrix-legend {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
}

.matrix-legend h4 {
    color: #4a5568;
    margin-bottom: 15px;
    text-align: center;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-sample {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.high-attention {
    background: rgba(102, 126, 234, 0.9);
}

.medium-attention {
    background: rgba(102, 126, 234, 0.5);
}

.low-attention {
    background: rgba(102, 126, 234, 0.1);
}

.attention-insights {
    background: #f0fff4;
    padding: 25px;
    border-radius: 12px;
    border-left: 5px solid #48bb78;
}

.attention-insights h4 {
    color: #2f855a;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-align: center;
}

.insight-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.insight-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.insight-card:hover {
    transform: translateY(-3px);
}

.insight-card h5 {
    color: #2f855a;
    margin-bottom: 12px;
    font-size: 1.1em;
}

.insight-card p {
    color: #4a5568;
    line-height: 1.6;
}

/* 理解区域 */
.understanding-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.understanding-section h2 {
    color: #2d3748;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-align: center;
}

.understanding-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.concept-card {
    background: #f7fafc;
    padding: 25px;
    border-radius: 12px;
    border-left: 5px solid #667eea;
    transition: transform 0.3s ease;
}

.concept-card:hover {
    transform: translateY(-3px);
}

.concept-card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.concept-card p,
.concept-card li {
    color: #718096;
    line-height: 1.7;
    margin-bottom: 10px;
}

.concept-card ul {
    margin-left: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2em;
    }

    #sentence-input {
        width: 100%;
        margin-bottom: 15px;
        margin-right: 0;
    }

    .matrices-container {
        grid-template-columns: 1fr;
    }

    .understanding-content {
        grid-template-columns: 1fr;
    }

    .example-matrix-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .example-attention-matrix {
        grid-template-columns: repeat(9, 1fr);
        font-size: 0.8em;
        max-width: 100%;
        overflow-x: auto;
    }

    .example-cell {
        min-height: 30px;
        font-size: 0.7em;
        padding: 4px 2px;
    }

    .insight-cards {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1s ease-in-out infinite;
}
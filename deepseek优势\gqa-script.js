// GQA演示脚本
class GQADemo {
    constructor() {
        this.currentStep = 0;
        this.totalSteps = 4;
        this.isRunning = false;
        this.currentMode = 'mha'; // 'mha' 或 'gqa'

        // 步骤数据
        this.mhaSteps = [
            {
                title: '步骤1: 输入处理',
                description: '传统MHA为每个查询分别计算Key和Value',
                knowledge: this.getMHAStep1Knowledge(),
                visualization: this.createMHAStep1Visualization
            },
            {
                title: '步骤2: 注意力计算',
                description: '每个头独立计算完整的注意力矩阵',
                knowledge: this.getMHAStep2Knowledge(),
                visualization: this.createMHAStep2Visualization
            },
            {
                title: '步骤3: 多头并行',
                description: '8个注意力头并行处理，计算量巨大',
                knowledge: this.getMHAStep3Knowledge(),
                visualization: this.createMHAStep3Visualization
            },
            {
                title: '步骤4: 结果合并',
                description: '合并所有头的输出，资源消耗高',
                knowledge: this.getMHAStep4Knowledge(),
                visualization: this.createMHAStep4Visualization
            }
        ];

        this.gqaSteps = [
            {
                title: '步骤1: 查询分组',
                description: 'GQA将相似查询分组，减少冗余计算',
                knowledge: this.getGQAStep1Knowledge(),
                visualization: this.createGQAStep1Visualization
            },
            {
                title: '步骤2: 共享K&V',
                description: '同组查询共享Key和Value，大幅降低计算量',
                knowledge: this.getGQAStep2Knowledge(),
                visualization: this.createGQAStep2Visualization
            },
            {
                title: '步骤3: 高效计算',
                description: '分组处理显著提升计算效率',
                knowledge: this.getGQAStep3Knowledge(),
                visualization: this.createGQAStep3Visualization
            },
            {
                title: '步骤4: 性能对比',
                description: 'GQA在保持精度的同时大幅提升性能',
                knowledge: this.getGQAStep4Knowledge(),
                visualization: this.createGQAStep4Visualization
            }
        ];

        this.initializeElements();
        this.bindEvents();
        this.updateArchitecture();
    }

    initializeElements() {
        // 模式切换按钮
        this.mhaModeBtn = document.getElementById('mhaMode');
        this.gqaModeBtn = document.getElementById('gqaMode');

        // 演示控制按钮
        this.startBtn = document.getElementById('startDemo');
        this.resetBtn = document.getElementById('resetDemo');
        this.nextBtn = document.getElementById('nextStep');

        // 界面元素
        this.progressFill = document.getElementById('progressFill');
        this.stepCounter = document.getElementById('stepCounter');
        this.architectureTitle = document.getElementById('architectureTitle');
        this.architectureContainer = document.getElementById('architectureContainer');
        this.stepTitle = document.getElementById('stepTitle');
        this.stepDescription = document.getElementById('stepDescription');
        this.stepVisualization = document.getElementById('stepVisualization');
        this.knowledgeContent = document.getElementById('knowledgeContent');

        // 性能指标
        this.complexityValue = document.getElementById('complexityValue');
        this.memoryValue = document.getElementById('memoryValue');
        this.speedValue = document.getElementById('speedValue');
    }

    bindEvents() {
        // 模式切换事件
        this.mhaModeBtn.addEventListener('click', () => this.switchMode('mha'));
        this.gqaModeBtn.addEventListener('click', () => this.switchMode('gqa'));

        // 演示控制事件
        this.startBtn.addEventListener('click', () => this.startDemo());
        this.resetBtn.addEventListener('click', () => this.resetDemo());
        this.nextBtn.addEventListener('click', () => this.nextStep());
    }

    switchMode(mode) {
        if (this.currentMode === mode) return;

        this.currentMode = mode;
        this.isRunning = false;
        this.currentStep = 0;

        // 更新模式按钮状态
        this.mhaModeBtn.classList.toggle('active', mode === 'mha');
        this.gqaModeBtn.classList.toggle('active', mode === 'gqa');

        // 更新架构标题和性能指标
        this.updateArchitecture();

        // 重置演示状态
        this.resetDemo();
    }

    updateArchitecture() {
        if (this.currentMode === 'mha') {
            this.architectureTitle.textContent = '传统多头注意力（MHA）';
            this.complexityValue.textContent = 'O(n²d×h)';
            this.memoryValue.textContent = '100%';
            this.speedValue.textContent = '1x';
            this.architectureContainer.style.borderColor = '#2196F3';
        } else {
            this.architectureTitle.textContent = '分组查询注意力（GQA）';
            this.complexityValue.textContent = 'O(n²d×g)';
            this.memoryValue.textContent = '25-50%';
            this.speedValue.textContent = '2-4x';
            this.architectureContainer.style.borderColor = '#4CAF50';
        }

        this.createArchitectureDiagram();
    }

    createArchitectureDiagram() {
        if (this.currentMode === 'mha') {
            this.architectureContainer.innerHTML = `
                <div style="text-align: center;">
                    <h4>传统MHA架构</h4>
                    <div style="display: flex; flex-direction: column; gap: 15px; align-items: center; margin: 20px 0;">
                        <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; border: 2px solid #2196F3;">
                            输入序列 (n × d)
                        </div>
                        <div style="font-size: 20px;">↓</div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 1<br>Q₁K₁V₁</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 2<br>Q₂K₂V₂</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 3<br>Q₃K₃V₃</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 4<br>Q₄K₄V₄</div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 5<br>Q₅K₅V₅</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 6<br>Q₆K₆V₆</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 7<br>Q₇K₇V₇</div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 6px; border: 1px solid #f44336; font-size: 12px;">Head 8<br>Q₈K₈V₈</div>
                        </div>
                        <div style="font-size: 20px;">↓</div>
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; border: 2px solid #4CAF50;">
                            合并输出 (n × d)
                        </div>
                    </div>
                    <p style="font-size: 14px; color: #666;">每个头独立计算，资源消耗大</p>
                </div>
            `;
        } else {
            this.architectureContainer.innerHTML = `
                <div style="text-align: center;">
                    <h4>GQA优化架构</h4>
                    <div style="display: flex; flex-direction: column; gap: 15px; align-items: center; margin: 20px 0;">
                        <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; border: 2px solid #2196F3;">
                            输入序列 (n × d)
                        </div>
                        <div style="font-size: 20px;">↓</div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border: 2px solid #FF9800;">
                            <strong>查询分组</strong>
                        </div>
                        <div style="font-size: 20px;">↓</div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                                <strong>组1</strong><br>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px; margin-top: 10px;">
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₁</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₂</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₃</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₄</div>
                                </div>
                                <div style="margin-top: 8px; font-size: 12px;">共享: K₁V₁</div>
                            </div>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                                <strong>组2</strong><br>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px; margin-top: 10px;">
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₅</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₆</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₇</div>
                                    <div style="background: #c8e6c9; padding: 6px; border-radius: 4px; font-size: 11px;">Q₈</div>
                                </div>
                                <div style="margin-top: 8px; font-size: 12px;">共享: K₂V₂</div>
                            </div>
                        </div>
                        <div style="font-size: 20px;">↓</div>
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; border: 2px solid #4CAF50;">
                            高效输出 (n × d)
                        </div>
                    </div>
                    <p style="font-size: 14px; color: #666;">分组共享，效率提升2-4倍</p>
                </div>
            `;
        }
    }

    getCurrentSteps() {
        return this.currentMode === 'mha' ? this.mhaSteps : this.gqaSteps;
    }

    startDemo() {
        this.isRunning = true;
        this.currentStep = 1;
        this.updateUI();
        this.showStep(1);

        this.startBtn.disabled = true;
        this.nextBtn.disabled = false;
    }

    resetDemo() {
        this.isRunning = false;
        this.currentStep = 0;
        this.updateUI();

        this.stepTitle.textContent = '选择模式开始演示';
        this.stepDescription.innerHTML = '<p>点击上方的模式切换按钮，然后点击"开始演示"来了解不同注意力机制的工作原理。</p>';
        this.stepVisualization.innerHTML = '';
        this.knowledgeContent.innerHTML = `
            <div class="welcome-knowledge">
                <h4>🎯 学习目标</h4>
                <ul>
                    <li>理解传统多头注意力机制的计算过程</li>
                    <li>掌握分组查询注意力（GQA）的优化原理</li>
                    <li>对比两种机制的性能差异</li>
                    <li>了解GQA在实际应用中的优势</li>
                </ul>
                
                <h4>📚 核心概念</h4>
                <div class="concept-box">
                    <p><strong>分组查询注意力（GQA）</strong>是DeepSeek等先进模型采用的优化技术，通过将相似的查询向量分组处理，显著降低计算复杂度。</p>
                </div>
                
                <h4>🔗 相关链接</h4>
                <p><a href="index.html" style="color: #4CAF50; text-decoration: none;">← 返回Transformer演示</a></p>
            </div>
        `;

        this.startBtn.disabled = false;
        this.nextBtn.disabled = true;
    }

    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.updateUI();
            this.showStep(this.currentStep);
        }
    }

    updateUI() {
        // 更新进度条
        const progress = (this.currentStep / this.totalSteps) * 100;
        this.progressFill.style.width = `${progress}%`;

        // 更新步骤计数器
        this.stepCounter.textContent = `步骤 ${this.currentStep}/${this.totalSteps}`;

        // 更新按钮状态
        this.nextBtn.disabled = this.currentStep >= this.totalSteps;
    }

    showStep(stepNumber) {
        const steps = this.getCurrentSteps();
        const step = steps[stepNumber - 1];

        // 更新标题和描述
        this.stepTitle.textContent = step.title;
        this.stepDescription.innerHTML = `<p>${step.description}</p>`;

        // 更新知识点内容
        this.knowledgeContent.innerHTML = step.knowledge;

        // 创建可视化
        step.visualization.call(this);

        // 添加淡入动画
        this.stepVisualization.classList.add('fade-in');
        setTimeout(() => {
            this.stepVisualization.classList.remove('fade-in');
        }, 500);
    }

    // MHA知识点内容
    getMHAStep1Knowledge() {
        return `
            <h4>🔵 传统多头注意力 - 输入处理</h4>
            <div class="concept-box">
                <p><strong>核心特点：</strong>每个注意力头都需要独立计算自己的Query、Key、Value矩阵</p>
            </div>

            <h4>📊 计算过程</h4>
            <ul>
                <li><strong>8个注意力头：</strong>每个头都有独立的参数矩阵</li>
                <li><strong>参数量：</strong>W_Q, W_K, W_V × 8 = 24个矩阵</li>
                <li><strong>计算量：</strong>每个头都要完整计算一遍</li>
            </ul>

            <h4>⚠️ 问题分析</h4>
            <div class="concept-box">
                <p>传统MHA虽然表达能力强，但存在大量冗余计算，特别是在处理长序列时，计算复杂度呈平方增长。</p>
            </div>
        `;
    }

    getMHAStep2Knowledge() {
        return `
            <h4>🔵 传统MHA - 注意力计算</h4>
            <div class="concept-box">
                <p><strong>计算公式：</strong>Attention(Q,K,V) = softmax(QK^T/√d_k)V</p>
            </div>

            <h4>📈 复杂度分析</h4>
            <ul>
                <li><strong>时间复杂度：</strong>O(n²d) × 8头 = O(8n²d)</li>
                <li><strong>空间复杂度：</strong>需要存储8个完整的注意力矩阵</li>
                <li><strong>内存占用：</strong>随序列长度平方增长</li>
            </ul>

            <h4>🎯 实际影响</h4>
            <div class="concept-box">
                <p>当序列长度为2048时，需要计算8个2048×2048的注意力矩阵，内存和计算需求巨大。</p>
            </div>
        `;
    }

    getMHAStep3Knowledge() {
        return `
            <h4>🔵 传统MHA - 多头并行</h4>
            <div class="concept-box">
                <p><strong>并行策略：</strong>8个注意力头同时计算，每个头处理不同的表示子空间</p>
            </div>

            <h4>💡 设计理念</h4>
            <ul>
                <li><strong>多样性：</strong>不同头关注不同类型的关系</li>
                <li><strong>表达力：</strong>组合多个头的输出获得丰富表示</li>
                <li><strong>并行性：</strong>可以并行计算提高速度</li>
            </ul>

            <h4>⚡ 资源消耗</h4>
            <div class="concept-box">
                <p>虽然并行计算提高了速度，但总的计算量和内存需求仍然很大，特别是在资源受限的环境中。</p>
            </div>
        `;
    }

    getMHAStep4Knowledge() {
        return `
            <h4>🔵 传统MHA - 结果合并</h4>
            <div class="concept-box">
                <p><strong>合并过程：</strong>将8个头的输出拼接后通过线性变换得到最终结果</p>
            </div>

            <h4>📋 总结特点</h4>
            <ul>
                <li><strong>优点：</strong>表达能力强，能捕获复杂的依赖关系</li>
                <li><strong>缺点：</strong>计算复杂度高，内存消耗大</li>
                <li><strong>适用：</strong>计算资源充足的场景</li>
            </ul>

            <h4>🚀 优化需求</h4>
            <div class="concept-box">
                <p>在实际部署中，特别是移动设备或边缘计算场景，需要在保持性能的同时降低计算成本，这就是GQA技术的价值所在。</p>
            </div>
        `;
    }

    // GQA知识点内容
    getGQAStep1Knowledge() {
        return `
            <h4>🟢 分组查询注意力 - 查询分组</h4>
            <div class="concept-box">
                <p><strong>核心创新：</strong>将8个查询头分成2-4组，同组内的查询共享Key和Value</p>
            </div>

            <h4>🎯 分组策略</h4>
            <ul>
                <li><strong>智能分组：</strong>将语义相似的查询归为一组</li>
                <li><strong>参数共享：</strong>同组查询使用相同的K、V矩阵</li>
                <li><strong>效率提升：</strong>减少重复计算，降低内存占用</li>
            </ul>

            <h4>📘 生活类比</h4>
            <div class="concept-box">
                <p>就像餐厅将点相同菜品的顾客分组处理，而不是每个顾客都单独准备一套厨具。</p>
            </div>
        `;
    }

    getGQAStep2Knowledge() {
        return `
            <h4>🟢 GQA - 共享Key&Value</h4>
            <div class="concept-box">
                <p><strong>共享机制：</strong>4个查询头共享1组K、V，将参数量从24个矩阵减少到10个</p>
            </div>

            <h4>⚡ 效率提升</h4>
            <ul>
                <li><strong>参数减少：</strong>K、V矩阵数量减少75%</li>
                <li><strong>计算优化：</strong>避免重复的K、V计算</li>
                <li><strong>内存节省：</strong>显著降低内存占用</li>
            </ul>

            <h4>🎯 技术优势</h4>
            <div class="concept-box">
                <p>通过智能的参数共享，GQA在保持模型表达能力的同时，大幅降低了计算和存储成本。</p>
            </div>
        `;
    }

    getGQAStep3Knowledge() {
        return `
            <h4>🟢 GQA - 高效计算</h4>
            <div class="concept-box">
                <p><strong>计算优化：</strong>时间复杂度从O(8n²d)降低到O(2n²d)，提升4倍效率</p>
            </div>

            <h4>📊 性能对比</h4>
            <ul>
                <li><strong>计算量：</strong>减少60-75%</li>
                <li><strong>内存占用：</strong>降低50-75%</li>
                <li><strong>推理速度：</strong>提升2-4倍</li>
                <li><strong>精度损失：</strong>几乎可以忽略</li>
            </ul>

            <h4>🚀 实际应用</h4>
            <div class="concept-box">
                <p>DeepSeek等先进模型采用GQA技术，在保持高质量输出的同时，显著提升了推理效率和用户体验。</p>
            </div>
        `;
    }

    getGQAStep4Knowledge() {
        return `
            <h4>🟢 GQA - 性能对比总结</h4>
            <div class="concept-box">
                <p><strong>技术成果：</strong>GQA成功实现了效率与性能的平衡，是现代大语言模型的关键优化技术</p>
            </div>

            <h4>📈 量化收益</h4>
            <ul>
                <li><strong>推理速度：</strong>提升2-4倍</li>
                <li><strong>内存使用：</strong>减少25-50%</li>
                <li><strong>能耗降低：</strong>显著减少计算资源消耗</li>
                <li><strong>部署成本：</strong>降低硬件要求</li>
            </ul>

            <h4>🌟 技术意义</h4>
            <div class="concept-box">
                <p>GQA技术使得大规模语言模型能够在更多场景下部署，从云端到边缘设备，推动了AI技术的普及和应用。</p>
            </div>

            <h4>🔮 未来展望</h4>
            <ul>
                <li>更智能的分组策略</li>
                <li>动态调整分组数量</li>
                <li>与其他优化技术结合</li>
            </ul>
        `;
    }

    // MHA可视化函数
    createMHAStep1Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>传统MHA - 独立计算每个头</h4>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>问题：</strong>每个注意力头都需要独立的Q、K、V矩阵</p>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 20px 0;">
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 1</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₁ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₁ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₁ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 2</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₂ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₂ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₂ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 3</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₃ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₃ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₃ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 4</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₄ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₄ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₄ 矩阵</div>
                            </div>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 5</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₅ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₅ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₅ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 6</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₆ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₆ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₆ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 7</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₇ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₇ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₇ 矩阵</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>Head 8</strong><br>
                            <div style="margin: 10px 0; font-size: 12px;">
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">Q₈ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">K₈ 矩阵</div>
                                <div style="background: #ffcdd2; padding: 4px; margin: 2px 0; border-radius: 4px;">V₈ 矩阵</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin-top: 15px; border: 2px solid #f44336;">
                        <strong>总计：24个参数矩阵</strong><br>
                        <span style="font-size: 14px; color: #666;">每个头都需要独立的Q、K、V矩阵</span>
                    </div>
                </div>
            </div>
        `;
    }

    createMHAStep2Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>传统MHA - 注意力矩阵计算</h4>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>每个头都要计算完整的注意力矩阵</strong></p>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
                        <div style="background: #ffebee; padding: 20px; border-radius: 8px; border: 2px solid #f44336;">
                            <h5>Head 1 注意力矩阵</h5>
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 3px; margin: 10px 0;">
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.8</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.2</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.6</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.2</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.3</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.5</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.2</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.7</div>
                            </div>
                        </div>
                        <div style="background: #ffebee; padding: 20px; border-radius: 8px; border: 2px solid #f44336;">
                            <h5>Head 2 注意力矩阵</h5>
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 3px; margin: 10px 0;">
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.7</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.2</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.8</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.2</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.6</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.1</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.0</div>
                                <div style="background: #ffcdd2; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.3</div>
                                <div style="background: #f44336; color: white; padding: 6px; text-align: center; border-radius: 3px; font-size: 11px;">0.6</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                        <p><strong>需要计算8个这样的矩阵</strong></p>
                        <p style="font-size: 14px; color: #666;">每个矩阵大小：n×n，总计算量：O(8n²d)</p>
                    </div>
                </div>
            </div>
        `;
    }

    createMHAStep3Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>传统MHA - 8头并行计算</h4>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>所有头同时进行计算，资源消耗巨大</strong></p>
                    </div>
                    <div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px;">
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H1</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H2</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H3</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H4</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H5</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H6</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H7</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                            <div style="background: #ffebee; padding: 12px; border-radius: 6px; border: 2px solid #f44336; text-align: center; position: relative;">
                                <div style="position: absolute; top: -5px; right: -5px; background: #f44336; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px;">⚡</div>
                                <strong>H8</strong><br>
                                <div style="font-size: 10px; margin-top: 5px;">计算中...</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #ffebee; padding: 20px; border-radius: 8px; border: 2px solid #f44336;">
                        <h5>资源消耗统计</h5>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 15px;">
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>计算单元</strong><br>
                                <span style="color: #f44336; font-size: 18px;">8个</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>内存占用</strong><br>
                                <span style="color: #f44336; font-size: 18px;">100%</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px;">
                                <strong>计算复杂度</strong><br>
                                <span style="color: #f44336; font-size: 18px;">O(8n²d)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createMHAStep4Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>传统MHA - 结果合并</h4>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 15px; margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px;">
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H1输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.2, 0.8, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H2输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.5, 0.3, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H3输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.1, 0.7, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H4输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.9, 0.1, ...]</div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px;">
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H5输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.4, 0.6, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H6输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.7, 0.2, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H7输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.3, 0.8, ...]</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 6px; border: 2px solid #f44336; text-align: center;">
                                <strong>H8输出</strong><br>
                                <div style="font-size: 11px; margin-top: 5px;">[0.6, 0.4, ...]</div>
                            </div>
                        </div>
                        <div style="font-size: 24px; color: #666;">↓</div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border: 2px solid #FF9800;">
                            <strong>拼接 (Concatenate)</strong><br>
                            <div style="font-size: 12px; margin-top: 5px;">
                                [H1, H2, H3, H4, H5, H6, H7, H8] → 8d维向量
                            </div>
                        </div>
                        <div style="font-size: 24px; color: #666;">↓</div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <strong>线性变换 W_O</strong><br>
                            <div style="font-size: 12px; margin-top: 5px;">
                                8d → d维最终输出
                            </div>
                        </div>
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                        <p><strong>传统MHA总结</strong></p>
                        <ul style="text-align: left; margin: 10px 0;">
                            <li>表达能力强，但计算成本高</li>
                            <li>需要大量计算资源和内存</li>
                            <li>在长序列处理时性能瓶颈明显</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    // GQA可视化函数
    createGQAStep1Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>GQA - 智能查询分组</h4>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>核心创新：</strong>将8个查询头分成2组，每组共享K、V</p>
                    </div>
                    <div style="display: flex; justify-content: center; gap: 30px; margin: 20px 0;">
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border: 3px solid #4CAF50;">
                            <h5 style="color: #4CAF50; margin-bottom: 15px;">🟢 组1 (语义相似)</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 15px;">
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #4CAF50;">
                                    <strong>Q₁</strong><br>
                                    <div style="font-size: 11px;">语法关系</div>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #4CAF50;">
                                    <strong>Q₂</strong><br>
                                    <div style="font-size: 11px;">句法结构</div>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #4CAF50;">
                                    <strong>Q₃</strong><br>
                                    <div style="font-size: 11px;">词性分析</div>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #4CAF50;">
                                    <strong>Q₄</strong><br>
                                    <div style="font-size: 11px;">语法依赖</div>
                                </div>
                            </div>
                            <div style="background: #a5d6a7; padding: 10px; border-radius: 6px; text-align: center;">
                                <strong>共享：K₁ & V₁</strong><br>
                                <div style="font-size: 11px;">语法特征提取</div>
                            </div>
                        </div>

                        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border: 3px solid #2196F3;">
                            <h5 style="color: #2196F3; margin-bottom: 15px;">🔵 组2 (语义相似)</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 15px;">
                                <div style="background: #bbdefb; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #2196F3;">
                                    <strong>Q₅</strong><br>
                                    <div style="font-size: 11px;">语义理解</div>
                                </div>
                                <div style="background: #bbdefb; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #2196F3;">
                                    <strong>Q₆</strong><br>
                                    <div style="font-size: 11px;">上下文</div>
                                </div>
                                <div style="background: #bbdefb; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #2196F3;">
                                    <strong>Q₇</strong><br>
                                    <div style="font-size: 11px;">词汇含义</div>
                                </div>
                                <div style="background: #bbdefb; padding: 10px; border-radius: 6px; text-align: center; border: 2px solid #2196F3;">
                                    <strong>Q₈</strong><br>
                                    <div style="font-size: 11px;">语义关联</div>
                                </div>
                            </div>
                            <div style="background: #90caf9; padding: 10px; border-radius: 6px; text-align: center;">
                                <strong>共享：K₂ & V₂</strong><br>
                                <div style="font-size: 11px;">语义特征提取</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                        <p><strong>分组优势：</strong>参数量从24个矩阵减少到10个矩阵</p>
                        <p style="font-size: 14px; color: #666;">8个Q + 2个K + 2个V = 12个矩阵（相比传统的24个）</p>
                    </div>
                </div>
            </div>
        `;
    }

    createGQAStep2Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>GQA - 共享Key&Value机制</h4>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>核心机制：</strong>同组查询共享相同的K、V矩阵，大幅减少计算量</p>
                    </div>
                    <div style="display: flex; justify-content: center; gap: 20px; margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 2px solid #ddd;">
                            <h5>传统MHA</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px; margin: 10px 0;">
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₁K₁V₁</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₂K₂V₂</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₃K₃V₃</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₄K₄V₄</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₅K₅V₅</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₆K₆V₆</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₇K₇V₇</div>
                                <div style="background: #ffcdd2; padding: 6px; border-radius: 4px; font-size: 11px;">Q₈K₈V₈</div>
                            </div>
                            <div style="background: #ffebee; padding: 8px; border-radius: 4px; margin-top: 10px;">
                                <strong>总计：24个矩阵</strong>
                            </div>
                        </div>

                        <div style="font-size: 30px; color: #4CAF50; display: flex; align-items: center;">→</div>

                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border: 2px solid #4CAF50;">
                            <h5>GQA优化</h5>
                            <div style="margin: 10px 0;">
                                <div style="background: #c8e6c9; padding: 8px; border-radius: 6px; margin-bottom: 8px;">
                                    <strong>组1：</strong>Q₁ Q₂ Q₃ Q₄ + <span style="color: #4CAF50;">共享K₁V₁</span>
                                </div>
                                <div style="background: #bbdefb; padding: 8px; border-radius: 6px;">
                                    <strong>组2：</strong>Q₅ Q₆ Q₇ Q₈ + <span style="color: #2196F3;">共享K₂V₂</span>
                                </div>
                            </div>
                            <div style="background: #c8e6c9; padding: 8px; border-radius: 4px; margin-top: 10px;">
                                <strong>总计：12个矩阵</strong>
                            </div>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 20px;">
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <h6>参数减少</h6>
                            <div style="font-size: 24px; color: #4CAF50; font-weight: bold;">50%</div>
                            <div style="font-size: 12px; color: #666;">24→12矩阵</div>
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <h6>计算优化</h6>
                            <div style="font-size: 24px; color: #4CAF50; font-weight: bold;">75%</div>
                            <div style="font-size: 12px; color: #666;">K、V计算减少</div>
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <h6>内存节省</h6>
                            <div style="font-size: 24px; color: #4CAF50; font-weight: bold;">60%</div>
                            <div style="font-size: 12px; color: #666;">存储需求降低</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createGQAStep3Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>GQA - 高效计算过程</h4>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>计算效率：</strong>通过分组共享，计算复杂度显著降低</p>
                    </div>
                    <div style="display: flex; justify-content: center; gap: 30px; margin: 20px 0;">
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border: 3px solid #4CAF50;">
                            <h5 style="color: #4CAF50;">🟢 组1高效计算</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin: 15px 0;">
                                <div style="background: #c8e6c9; padding: 8px; border-radius: 4px; text-align: center;">Q₁</div>
                                <div style="background: #c8e6c9; padding: 8px; border-radius: 4px; text-align: center;">Q₂</div>
                                <div style="background: #c8e6c9; padding: 8px; border-radius: 4px; text-align: center;">Q₃</div>
                                <div style="background: #c8e6c9; padding: 8px; border-radius: 4px; text-align: center;">Q₄</div>
                            </div>
                            <div style="font-size: 20px; margin: 10px 0;">×</div>
                            <div style="background: #a5d6a7; padding: 10px; border-radius: 6px; text-align: center;">
                                <strong>K₁ & V₁</strong><br>
                                <div style="font-size: 11px;">一次计算，四次使用</div>
                            </div>
                            <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin-top: 10px;">
                                <strong>计算量：</strong>O(n²d) × 1
                            </div>
                        </div>

                        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border: 3px solid #2196F3;">
                            <h5 style="color: #2196F3;">🔵 组2高效计算</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin: 15px 0;">
                                <div style="background: #bbdefb; padding: 8px; border-radius: 4px; text-align: center;">Q₅</div>
                                <div style="background: #bbdefb; padding: 8px; border-radius: 4px; text-align: center;">Q₆</div>
                                <div style="background: #bbdefb; padding: 8px; border-radius: 4px; text-align: center;">Q₇</div>
                                <div style="background: #bbdefb; padding: 8px; border-radius: 4px; text-align: center;">Q₈</div>
                            </div>
                            <div style="font-size: 20px; margin: 10px 0;">×</div>
                            <div style="background: #90caf9; padding: 10px; border-radius: 6px; text-align: center;">
                                <strong>K₂ & V₂</strong><br>
                                <div style="font-size: 11px;">一次计算，四次使用</div>
                            </div>
                            <div style="background: #bbdefb; padding: 10px; border-radius: 6px; margin-top: 10px;">
                                <strong>计算量：</strong>O(n²d) × 1
                            </div>
                        </div>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border: 2px solid #4CAF50;">
                        <h5>性能对比</h5>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 15px;">
                            <div style="background: #ffebee; padding: 15px; border-radius: 6px;">
                                <h6>传统MHA</h6>
                                <ul style="text-align: left; font-size: 14px; margin: 10px 0;">
                                    <li>计算复杂度：O(8n²d)</li>
                                    <li>内存占用：100%</li>
                                    <li>推理速度：1x</li>
                                </ul>
                            </div>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 6px;">
                                <h6>GQA优化</h6>
                                <ul style="text-align: left; font-size: 14px; margin: 10px 0;">
                                    <li>计算复杂度：O(2n²d)</li>
                                    <li>内存占用：25-50%</li>
                                    <li>推理速度：2-4x</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createGQAStep4Visualization() {
        this.stepVisualization.innerHTML = `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <div style="text-align: center;">
                    <h4>GQA vs MHA - 全面性能对比</h4>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <p><strong>技术成果：</strong>GQA在保持精度的同时，显著提升了计算效率</p>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 25px; margin: 20px 0;">
                        <div style="background: #ffebee; padding: 20px; border-radius: 10px; border: 3px solid #f44336;">
                            <h5 style="color: #f44336;">🔴 传统MHA</h5>
                            <div style="margin: 15px 0;">
                                <div style="background: #ffcdd2; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>参数矩阵：</strong>24个
                                </div>
                                <div style="background: #ffcdd2; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>计算复杂度：</strong>O(8n²d)
                                </div>
                                <div style="background: #ffcdd2; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>内存占用：</strong>100%
                                </div>
                                <div style="background: #ffcdd2; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>推理速度：</strong>1x
                                </div>
                                <div style="background: #ffcdd2; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>能耗：</strong>高
                                </div>
                            </div>
                            <div style="background: #f44336; color: white; padding: 8px; border-radius: 4px;">
                                适用：资源充足场景
                            </div>
                        </div>

                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border: 3px solid #4CAF50;">
                            <h5 style="color: #4CAF50;">🟢 GQA优化</h5>
                            <div style="margin: 15px 0;">
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>参数矩阵：</strong>12个 <span style="color: #4CAF50;">(-50%)</span>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>计算复杂度：</strong>O(2n²d) <span style="color: #4CAF50;">(-75%)</span>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>内存占用：</strong>25-50% <span style="color: #4CAF50;">(-50%)</span>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>推理速度：</strong>2-4x <span style="color: #4CAF50;">(+300%)</span>
                                </div>
                                <div style="background: #c8e6c9; padding: 10px; border-radius: 6px; margin: 5px 0;">
                                    <strong>能耗：</strong>低 <span style="color: #4CAF50;">(-60%)</span>
                                </div>
                            </div>
                            <div style="background: #4CAF50; color: white; padding: 8px; border-radius: 4px;">
                                适用：广泛部署场景
                            </div>
                        </div>
                    </div>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border: 2px solid #FF9800;">
                        <h5>🚀 实际应用价值</h5>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 15px;">
                            <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 5px;">📱</div>
                                <strong>移动设备</strong><br>
                                <span style="font-size: 12px; color: #666;">资源受限环境</span>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 5px;">⚡</div>
                                <strong>实时推理</strong><br>
                                <span style="font-size: 12px; color: #666;">低延迟需求</span>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 5px;">🌍</div>
                                <strong>大规模部署</strong><br>
                                <span style="font-size: 12px; color: #666;">成本控制</span>
                            </div>
                        </div>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50; margin-top: 15px;">
                        <p><strong>🌟 DeepSeek的成功实践</strong></p>
                        <p style="font-size: 14px; color: #666; margin-top: 5px;">
                            通过GQA技术，DeepSeek等先进模型在保持高质量输出的同时，实现了显著的效率提升，
                            使得大规模语言模型能够在更多场景下实际部署和应用。
                        </p>
                    </div>
                </div>
            </div>
        `;
    }
}

// 初始化演示
document.addEventListener('DOMContentLoaded', () => {
    new GQADemo();
});

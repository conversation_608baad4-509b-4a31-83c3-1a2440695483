/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 控制面板 */
.control-panel {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary,
.btn-step {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(45deg, #9C27B0, #7B1FA2);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #f44336, #da190b);
    color: white;
}

.btn-step {
    background: linear-gradient(45deg, #FF5722, #E64A19);
    color: white;
}

.btn-primary:hover,
.btn-secondary:hover,
.btn-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-step:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin: 0 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #9C27B0, #7B1FA2);
    width: 0%;
    transition: width 0.5s ease;
}

.step-counter {
    font-weight: 600;
    color: #666;
}

/* 三栏布局 */
.three-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 25px;
    grid-column: 1 / -1;
}

/* 架构概览 */
.architecture-overview {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #9C27B0;
}

.architecture-overview h3 {
    text-align: center;
    margin-bottom: 25px;
    color: #333;
    font-size: 1.3em;
}

.decoder-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.component {
    background: linear-gradient(45deg, #f3e5f5, #e1bee7);
    border: 2px solid #9C27B0;
    border-radius: 12px;
    padding: 15px 20px;
    min-width: 250px;
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
}

.component:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(156, 39, 176, 0.3);
}

.component.active {
    background: linear-gradient(45deg, #9C27B0, #7B1FA2);
    color: white;
    border-color: #9C27B0;
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(156, 39, 176, 0.4);
}

.arrow {
    font-size: 24px;
    color: #666;
    font-weight: bold;
}

/* 中间可视化面板 */
.step-visualization-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #FF5722;
}

#stepTitle {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.step-description {
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-visualization {
    min-height: 300px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px dashed #ddd;
}

/* 右侧知识点面板 */
.knowledge-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4CAF50;
}

.knowledge-panel h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-content {
    line-height: 1.6;
}

.welcome-knowledge h4 {
    color: #4CAF50;
    margin: 15px 0 10px 0;
    font-size: 1.1em;
}

.welcome-knowledge ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.welcome-knowledge li {
    margin-bottom: 5px;
}

/* 示例框样式 */
.example-box {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
}

.example-box p {
    margin-bottom: 10px;
}

.example-box ul,
.example-box ol {
    margin-left: 20px;
    margin-bottom: 10px;
}

.example-box li {
    margin-bottom: 5px;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1s infinite;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .three-column-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .knowledge-panel {
        border-left: none;
        border-top: 4px solid #4CAF50;
    }

    .step-visualization-panel {
        border-left: none;
        border-top: 4px solid #FF5722;
    }

    .architecture-overview {
        border-left: none;
        border-top: 4px solid #9C27B0;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .control-panel {
        flex-direction: column;
        align-items: stretch;
    }

    .progress-bar {
        margin: 10px 0;
    }

    .component {
        min-width: 200px;
    }
}
/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    width: 95%;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 控制面板 */
.control-panel {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: space-between;
}

/* 模式切换按钮 */
.mode-switch {
    display: flex;
    gap: 5px;
    background: #f0f0f0;
    padding: 5px;
    border-radius: 10px;
}

.btn-mode {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    background: transparent;
    color: #666;
}

.btn-mode.active {
    background: white;
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-mode:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
}

/* 演示控制按钮 */
.demo-controls {
    display: flex;
    gap: 10px;
    flex: 1;
    justify-content: center;
}

.btn-primary,
.btn-secondary,
.btn-step {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #f44336, #da190b);
    color: white;
}

.btn-step {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
}

.btn-primary:hover,
.btn-secondary:hover,
.btn-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-step:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 进度区域 */
.progress-area {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
}

.progress-bar {
    flex: 1;
    height: 15px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    max-width: 200px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.5s ease;
}

.step-counter {
    font-weight: 600;
    color: #666;
}

/* 演示布局 */
.demo-layout {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 20px;
    grid-column: 1 / -1;
    width: 100%;
}

/* 架构面板 */
.architecture-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #2196F3;
}

.architecture-panel h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3em;
}

.architecture-container {
    min-height: 300px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px dashed #ddd;
}

/* 性能指标 */
.performance-metrics {
    background: #f0f8ff;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 600;
    color: #555;
}

.metric-value {
    font-weight: bold;
    color: #2196F3;
    font-family: 'Courier New', monospace;
}

/* 步骤演示面板 */
.step-demo-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #FF9800;
}

#stepTitle {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.step-description {
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-visualization {
    min-height: 350px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px dashed #ddd;
    overflow-y: auto;
    max-height: 500px;
}

/* 知识点面板 */
.knowledge-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4CAF50;
}

.knowledge-panel h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-content {
    line-height: 1.6;
    overflow-y: auto;
    max-height: 500px;
    padding-right: 10px;
}

.welcome-knowledge h4 {
    color: #4CAF50;
    margin: 15px 0 10px 0;
    font-size: 1.1em;
}

.welcome-knowledge ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.welcome-knowledge li {
    margin-bottom: 5px;
}

.concept-box {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
}

/* 类比说明区域 */
.analogy-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.analogy-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.4em;
    text-align: center;
}

.analogy-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
}

.analogy-card h4 {
    color: #FF9800;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.analogy-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.analogy-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.analogy-item h5 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.analogy-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background: #f0f0f0;
    border-radius: 6px;
}

.customer,
.waiter,
.group {
    font-size: 24px;
}

.arrow {
    font-size: 18px;
    color: #666;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1s infinite;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .demo-layout {
        grid-template-columns: 1fr 1.1fr 1fr;
        gap: 15px;
    }
}

@media (max-width: 1200px) {
    .demo-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .analogy-comparison {
        grid-template-columns: 1fr;
    }

    .container {
        width: 98%;
    }
}

@media (max-width: 768px) {
    .control-panel {
        flex-direction: column;
        align-items: stretch;
    }

    .demo-controls {
        flex-direction: column;
        gap: 10px;
    }

    .progress-area {
        justify-content: center;
    }

    .container {
        width: 100%;
        padding: 10px;
    }
}
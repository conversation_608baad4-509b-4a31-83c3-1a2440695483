// Transformer核心代码库 - 用于教学演示
// 每个环节的核心实现代码

const TransformerCoreCode = {
    // 1. 输入嵌入 (Input Embedding)
    embedding: {
        title: "🔍 输入嵌入核心代码",
        description: "将文本转换为数字向量的核心实现",
        code: `// 1. 词汇表构建和词汇到ID的映射
class Tokenizer {
    constructor(vocab) {
        this.vocab = vocab;  // 词汇表
        this.word_to_id = {};
        this.id_to_word = {};
        
        // 构建双向映射
        vocab.forEach((word, index) => {
            this.word_to_id[word] = index;
            this.id_to_word[index] = word;
        });
    }
    
    // 文本转换为ID序列
    encode(text) {
        return text.split('').map(char => 
            this.word_to_id[char] || this.word_to_id['<UNK>']
        );
    }
}

// 2. 嵌入层实现
class EmbeddingLayer {
    constructor(vocab_size, embed_dim) {
        this.vocab_size = vocab_size;
        this.embed_dim = embed_dim;
        
        // 初始化嵌入矩阵 (vocab_size × embed_dim)
        this.embedding_matrix = this.initializeMatrix(vocab_size, embed_dim);
    }
    
    // 随机初始化嵌入矩阵
    initializeMatrix(rows, cols) {
        const matrix = [];
        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                // 使用正态分布初始化
                row.push(this.randomNormal() * 0.02);
            }
            matrix.push(row);
        }
        return matrix;
    }
    
    // 前向传播：将token ID转换为向量
    forward(token_ids) {
        return token_ids.map(id => {
            if (id >= this.vocab_size) {
                throw new Error(\`Token ID \${id} 超出词汇表范围\`);
            }
            // 返回对应的嵌入向量
            return [...this.embedding_matrix[id]];
        });
    }
    
    // 生成正态分布随机数
    randomNormal() {
        let u = 0, v = 0;
        while(u === 0) u = Math.random();
        while(v === 0) v = Math.random();
        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    }
}

// 3. 使用示例
const vocab = ['我', '喜', '欢', '学', '习', 'A', 'I', '<UNK>', '<PAD>'];
const tokenizer = new Tokenizer(vocab);
const embedding = new EmbeddingLayer(vocab.length, 512);

// 处理输入文本
const text = "我喜欢AI";
const token_ids = tokenizer.encode(text);
const embeddings = embedding.forward(token_ids);

console.log("输入文本:", text);
console.log("Token IDs:", token_ids);
console.log("嵌入向量维度:", embeddings[0].length);`
    },

    // 2. 位置编码 (Positional Encoding)
    positional: {
        title: "📍 位置编码核心代码",
        description: "为序列添加位置信息的核心实现",
        code: `// 位置编码实现
class PositionalEncoding {
    constructor(max_len, embed_dim) {
        this.max_len = max_len;
        this.embed_dim = embed_dim;
        
        // 预计算位置编码矩阵
        this.pos_encoding = this.createPositionalEncoding();
    }
    
    // 创建位置编码矩阵
    createPositionalEncoding() {
        const pe = [];
        
        for (let pos = 0; pos < this.max_len; pos++) {
            const encoding = [];
            
            for (let i = 0; i < this.embed_dim; i++) {
                if (i % 2 === 0) {
                    // 偶数位置使用sin
                    const angle = pos / Math.pow(10000, i / this.embed_dim);
                    encoding.push(Math.sin(angle));
                } else {
                    // 奇数位置使用cos
                    const angle = pos / Math.pow(10000, (i - 1) / this.embed_dim);
                    encoding.push(Math.cos(angle));
                }
            }
            pe.push(encoding);
        }
        
        return pe;
    }
    
    // 添加位置编码到嵌入向量
    forward(embeddings) {
        const seq_len = embeddings.length;
        
        if (seq_len > this.max_len) {
            throw new Error(\`序列长度 \${seq_len} 超过最大长度 \${this.max_len}\`);
        }
        
        // 将位置编码加到嵌入向量上
        return embeddings.map((embedding, pos) => {
            return embedding.map((value, dim) => 
                value + this.pos_encoding[pos][dim]
            );
        });
    }
    
    // 获取特定位置的编码
    getPositionEncoding(position) {
        if (position >= this.max_len) {
            throw new Error(\`位置 \${position} 超出范围\`);
        }
        return [...this.pos_encoding[position]];
    }
    
    // 可视化位置编码模式
    visualizePattern(max_pos = 100) {
        console.log("位置编码模式 (前10个维度):");
        for (let pos = 0; pos < Math.min(max_pos, this.max_len); pos += 10) {
            const encoding = this.pos_encoding[pos].slice(0, 10);
            console.log(\`位置 \${pos}:\`, encoding.map(x => x.toFixed(3)));
        }
    }
}

// 使用示例
const max_sequence_length = 512;
const embedding_dimension = 512;
const pos_encoder = new PositionalEncoding(max_sequence_length, embedding_dimension);

// 假设我们有一些嵌入向量
const sample_embeddings = [
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1)
];

// 添加位置编码
const embeddings_with_pos = pos_encoder.forward(sample_embeddings);

console.log("原始嵌入维度:", sample_embeddings[0].length);
console.log("添加位置编码后维度:", embeddings_with_pos[0].length);
console.log("位置0的编码前5维:", pos_encoder.getPositionEncoding(0).slice(0, 5));`
    },

    // 3. 多头注意力 (Multi-Head Attention)
    attention: {
        title: "🎯 多头注意力核心代码",
        description: "计算序列内部关联关系的核心实现",
        code: `// 多头注意力机制实现
class MultiHeadAttention {
    constructor(embed_dim, num_heads) {
        this.embed_dim = embed_dim;
        this.num_heads = num_heads;
        this.head_dim = embed_dim / num_heads;

        if (embed_dim % num_heads !== 0) {
            throw new Error("embed_dim必须能被num_heads整除");
        }

        // 初始化权重矩阵
        this.W_q = this.initializeMatrix(embed_dim, embed_dim);  // Query权重
        this.W_k = this.initializeMatrix(embed_dim, embed_dim);  // Key权重
        this.W_v = this.initializeMatrix(embed_dim, embed_dim);  // Value权重
        this.W_o = this.initializeMatrix(embed_dim, embed_dim);  // 输出权重
    }

    // 初始化权重矩阵
    initializeMatrix(rows, cols) {
        const matrix = [];
        const scale = Math.sqrt(2.0 / (rows + cols));  // Xavier初始化

        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                row.push((Math.random() - 0.5) * 2 * scale);
            }
            matrix.push(row);
        }
        return matrix;
    }

    // 矩阵乘法
    matmul(A, B) {
        const result = [];
        for (let i = 0; i < A.length; i++) {
            const row = [];
            for (let j = 0; j < B[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < B.length; k++) {
                    sum += A[i][k] * B[k][j];
                }
                row.push(sum);
            }
            result.push(row);
        }
        return result;
    }

    // Softmax函数
    softmax(matrix) {
        return matrix.map(row => {
            const max_val = Math.max(...row);
            const exp_row = row.map(x => Math.exp(x - max_val));
            const sum_exp = exp_row.reduce((a, b) => a + b, 0);
            return exp_row.map(x => x / sum_exp);
        });
    }

    // 缩放点积注意力
    scaledDotProductAttention(Q, K, V) {
        // 计算注意力分数: Q * K^T
        const K_T = K[0].map((_, i) => K.map(row => row[i]));  // 转置K
        const scores = this.matmul(Q, K_T);

        // 缩放
        const scale = Math.sqrt(this.head_dim);
        const scaled_scores = scores.map(row =>
            row.map(score => score / scale)
        );

        // Softmax得到注意力权重
        const attention_weights = this.softmax(scaled_scores);

        // 加权求和: Attention * V
        const output = this.matmul(attention_weights, V);

        return { output, attention_weights };
    }

    // 前向传播
    forward(input_embeddings) {
        const seq_len = input_embeddings.length;

        // 计算Q, K, V
        const Q = this.matmul(input_embeddings, this.W_q);
        const K = this.matmul(input_embeddings, this.W_k);
        const V = this.matmul(input_embeddings, this.W_v);

        // 重塑为多头形状并计算注意力
        const all_head_outputs = [];
        const all_attention_weights = [];

        for (let head = 0; head < this.num_heads; head++) {
            const start_idx = head * this.head_dim;
            const end_idx = start_idx + this.head_dim;

            // 提取当前头的Q, K, V
            const Q_head = Q.map(row => row.slice(start_idx, end_idx));
            const K_head = K.map(row => row.slice(start_idx, end_idx));
            const V_head = V.map(row => row.slice(start_idx, end_idx));

            // 计算注意力
            const { output, attention_weights } =
                this.scaledDotProductAttention(Q_head, K_head, V_head);

            all_head_outputs.push(output);
            all_attention_weights.push(attention_weights);
        }

        // 拼接所有头的输出
        const concatenated = input_embeddings.map((_, i) => {
            const row = [];
            for (let head = 0; head < this.num_heads; head++) {
                row.push(...all_head_outputs[head][i]);
            }
            return row;
        });

        // 通过输出投影层
        const final_output = this.matmul(concatenated, this.W_o);

        return {
            output: final_output,
            attention_weights: all_attention_weights
        };
    }
}

// 使用示例
const embed_dim = 512;
const num_heads = 8;
const attention = new MultiHeadAttention(embed_dim, num_heads);

// 模拟输入序列 (seq_len=4, embed_dim=512)
const input_sequence = [
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1)
];

const { output, attention_weights } = attention.forward(input_sequence);

console.log("输入序列长度:", input_sequence.length);
console.log("输出序列长度:", output.length);
console.log("注意力头数:", attention_weights.length);
console.log("第一个头的注意力权重形状:",
    \`\${attention_weights[0].length} × \${attention_weights[0][0].length}\`);`
    },

    // 4. 前馈网络 (Feed Forward Network)
    feedforward: {
        title: "⚡ 前馈网络核心代码",
        description: "非线性变换和特征提取的核心实现",
        code: `// 前馈网络实现
class FeedForwardNetwork {
    constructor(embed_dim, hidden_dim, dropout_rate = 0.1) {
        this.embed_dim = embed_dim;
        this.hidden_dim = hidden_dim;
        this.dropout_rate = dropout_rate;

        // 初始化权重和偏置
        this.W1 = this.initializeMatrix(embed_dim, hidden_dim);    // 第一层权重
        this.b1 = new Array(hidden_dim).fill(0);                  // 第一层偏置
        this.W2 = this.initializeMatrix(hidden_dim, embed_dim);   // 第二层权重
        this.b2 = new Array(embed_dim).fill(0);                   // 第二层偏置
    }

    // 权重初始化 (He初始化，适用于ReLU)
    initializeMatrix(rows, cols) {
        const matrix = [];
        const scale = Math.sqrt(2.0 / rows);

        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                row.push(this.randomNormal() * scale);
            }
            matrix.push(row);
        }
        return matrix;
    }

    // 生成正态分布随机数
    randomNormal() {
        let u = 0, v = 0;
        while(u === 0) u = Math.random();
        while(v === 0) v = Math.random();
        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    }

    // ReLU激活函数
    relu(x) {
        return Math.max(0, x);
    }

    // GELU激活函数 (Transformer中常用)
    gelu(x) {
        return 0.5 * x * (1 + Math.tanh(Math.sqrt(2 / Math.PI) * (x + 0.044715 * Math.pow(x, 3))));
    }

    // 矩阵乘法
    matmul(A, B) {
        const result = [];
        for (let i = 0; i < A.length; i++) {
            const row = [];
            for (let j = 0; j < B[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < B.length; k++) {
                    sum += A[i][k] * B[k][j];
                }
                row.push(sum);
            }
            result.push(row);
        }
        return result;
    }

    // 向量加法 (广播)
    addBias(matrix, bias) {
        return matrix.map(row =>
            row.map((val, idx) => val + bias[idx])
        );
    }

    // Dropout (训练时使用)
    dropout(matrix, rate, training = true) {
        if (!training || rate === 0) {
            return matrix;
        }

        return matrix.map(row =>
            row.map(val => {
                if (Math.random() < rate) {
                    return 0;  // 随机置零
                } else {
                    return val / (1 - rate);  // 缩放补偿
                }
            })
        );
    }

    // 前向传播
    forward(input, training = false) {
        // 第一层: input -> hidden
        // X * W1 + b1
        let hidden = this.matmul(input, this.W1);
        hidden = this.addBias(hidden, this.b1);

        // 激活函数 (使用GELU)
        hidden = hidden.map(row =>
            row.map(val => this.gelu(val))
        );

        // Dropout (仅在训练时)
        hidden = this.dropout(hidden, this.dropout_rate, training);

        // 第二层: hidden -> output
        // Hidden * W2 + b2
        let output = this.matmul(hidden, this.W2);
        output = this.addBias(output, this.b2);

        return output;
    }

    // 获取参数数量
    getParameterCount() {
        const w1_params = this.embed_dim * this.hidden_dim;
        const b1_params = this.hidden_dim;
        const w2_params = this.hidden_dim * this.embed_dim;
        const b2_params = this.embed_dim;

        return {
            w1: w1_params,
            b1: b1_params,
            w2: w2_params,
            b2: b2_params,
            total: w1_params + b1_params + w2_params + b2_params
        };
    }
}

// 使用示例
const embed_dim = 512;
const hidden_dim = 2048;  // 通常是embed_dim的4倍
const ffn = new FeedForwardNetwork(embed_dim, hidden_dim);

// 模拟输入 (batch_size=4, seq_len=10, embed_dim=512)
const input_data = [];
for (let i = 0; i < 4; i++) {  // 4个序列
    const sequence = [];
    for (let j = 0; j < 10; j++) {  // 每个序列10个token
        sequence.push(new Array(512).fill(0).map(() => Math.random() * 0.1));
    }
    input_data.push(sequence);
}

// 处理每个序列
input_data.forEach((sequence, idx) => {
    const output = ffn.forward(sequence, false);  // 推理模式
    console.log(\`序列 \${idx + 1} - 输入形状: \${sequence.length} × \${sequence[0].length}\`);
    console.log(\`序列 \${idx + 1} - 输出形状: \${output.length} × \${output[0].length}\`);
});

console.log("FFN参数统计:", ffn.getParameterCount());`
    },

    // 5. 归一化和残差连接 (Layer Normalization & Residual Connection)
    normalization: {
        title: "🔄 归一化和残差连接核心代码",
        description: "稳定训练过程和信息传递的核心实现",
        code: `// 层归一化和残差连接实现
class LayerNormalization {
    constructor(embed_dim, eps = 1e-6) {
        this.embed_dim = embed_dim;
        this.eps = eps;  // 防止除零的小常数

        // 可学习参数
        this.gamma = new Array(embed_dim).fill(1.0);  // 缩放参数
        this.beta = new Array(embed_dim).fill(0.0);   // 偏移参数
    }

    // 计算均值
    mean(vector) {
        return vector.reduce((sum, val) => sum + val, 0) / vector.length;
    }

    // 计算方差
    variance(vector, mean_val) {
        const squared_diffs = vector.map(val => Math.pow(val - mean_val, 2));
        return squared_diffs.reduce((sum, val) => sum + val, 0) / vector.length;
    }

    // 层归一化前向传播
    forward(input) {
        return input.map(sequence_item => {
            // 对每个位置的特征向量进行归一化
            const mean_val = this.mean(sequence_item);
            const var_val = this.variance(sequence_item, mean_val);
            const std_val = Math.sqrt(var_val + this.eps);

            // 标准化: (x - μ) / σ
            const normalized = sequence_item.map(val => (val - mean_val) / std_val);

            // 应用可学习参数: γ * normalized + β
            return normalized.map((val, idx) =>
                this.gamma[idx] * val + this.beta[idx]
            );
        });
    }
}

// 残差连接实现
class ResidualConnection {
    constructor() {
        // 残差连接本身没有参数
    }

    // 残差连接: output = input + sublayer_output
    forward(input, sublayer_output) {
        if (input.length !== sublayer_output.length) {
            throw new Error("输入和子层输出的序列长度必须相同");
        }

        return input.map((input_item, i) => {
            if (input_item.length !== sublayer_output[i].length) {
                throw new Error("输入和子层输出的特征维度必须相同");
            }

            // 逐元素相加
            return input_item.map((val, j) => val + sublayer_output[i][j]);
        });
    }
}

// 组合层: LayerNorm + Sublayer + Residual
class LayerNormResidual {
    constructor(embed_dim, eps = 1e-6) {
        this.layer_norm = new LayerNormalization(embed_dim, eps);
        this.residual = new ResidualConnection();
    }

    // Pre-LN: LayerNorm -> Sublayer -> Residual
    forwardPreLN(input, sublayer_function) {
        // 1. 先进行层归一化
        const normalized_input = this.layer_norm.forward(input);

        // 2. 通过子层 (注意力或前馈网络)
        const sublayer_output = sublayer_function(normalized_input);

        // 3. 残差连接
        return this.residual.forward(input, sublayer_output);
    }

    // Post-LN: Sublayer -> Residual -> LayerNorm
    forwardPostLN(input, sublayer_function) {
        // 1. 通过子层
        const sublayer_output = sublayer_function(input);

        // 2. 残差连接
        const residual_output = this.residual.forward(input, sublayer_output);

        // 3. 层归一化
        return this.layer_norm.forward(residual_output);
    }
}

// 使用示例
const embed_dim = 512;
const layer_norm_residual = new LayerNormResidual(embed_dim);

// 模拟输入序列
const input_sequence = [
    new Array(512).fill(0).map(() => Math.random() * 2 - 1),  // [-1, 1]范围
    new Array(512).fill(0).map(() => Math.random() * 2 - 1),
    new Array(512).fill(0).map(() => Math.random() * 2 - 1)
];

// 模拟子层函数 (例如注意力层的简化版本)
function mockSublayer(input) {
    // 简单的线性变换作为示例
    return input.map(sequence_item =>
        sequence_item.map(val => val * 0.8 + 0.1)  // 简单变换
    );
}

// 测试Pre-LN
const output_pre_ln = layer_norm_residual.forwardPreLN(input_sequence, mockSublayer);

// 测试Post-LN
const output_post_ln = layer_norm_residual.forwardPostLN(input_sequence, mockSublayer);

console.log("输入序列形状:", \`\${input_sequence.length} × \${input_sequence[0].length}\`);
console.log("Pre-LN输出形状:", \`\${output_pre_ln.length} × \${output_pre_ln[0].length}\`);
console.log("Post-LN输出形状:", \`\${output_post_ln.length} × \${output_post_ln[0].length}\`);

// 验证残差连接的效果
const input_mean = input_sequence[0].reduce((a, b) => a + b, 0) / input_sequence[0].length;
const output_mean = output_pre_ln[0].reduce((a, b) => a + b, 0) / output_pre_ln[0].length;
console.log("输入均值:", input_mean.toFixed(4));
console.log("输出均值:", output_mean.toFixed(4));`
    },

    // 6. 输出预测 (Output Prediction)
    output: {
        title: "🎯 输出预测核心代码",
        description: "将隐藏状态转换为词汇概率分布的核心实现",
        code: `// 输出预测层实现
class OutputPredictionLayer {
    constructor(embed_dim, vocab_size) {
        this.embed_dim = embed_dim;
        this.vocab_size = vocab_size;

        // 输出投影矩阵 (通常与输入嵌入矩阵共享权重)
        this.output_projection = this.initializeMatrix(embed_dim, vocab_size);
        this.bias = new Array(vocab_size).fill(0);
    }

    // 权重初始化
    initializeMatrix(rows, cols) {
        const matrix = [];
        const scale = Math.sqrt(1.0 / rows);

        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                row.push((Math.random() - 0.5) * 2 * scale);
            }
            matrix.push(row);
        }
        return matrix;
    }

    // 矩阵乘法
    matmul(A, B) {
        const result = [];
        for (let i = 0; i < A.length; i++) {
            const row = [];
            for (let j = 0; j < B[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < B.length; k++) {
                    sum += A[i][k] * B[k][j];
                }
                row.push(sum);
            }
            result.push(row);
        }
        return result;
    }

    // Softmax函数 - 将logits转换为概率分布
    softmax(logits) {
        return logits.map(sequence_logits => {
            // 对每个位置的logits应用softmax
            const max_logit = Math.max(...sequence_logits);
            const exp_logits = sequence_logits.map(x => Math.exp(x - max_logit));
            const sum_exp = exp_logits.reduce((a, b) => a + b, 0);
            return exp_logits.map(x => x / sum_exp);
        });
    }

    // 温度缩放 - 控制输出分布的"尖锐度"
    temperatureScale(logits, temperature = 1.0) {
        if (temperature <= 0) {
            throw new Error("温度必须大于0");
        }

        return logits.map(sequence_logits =>
            sequence_logits.map(logit => logit / temperature)
        );
    }

    // Top-k采样 - 只考虑概率最高的k个词
    topKSampling(probabilities, k = 50) {
        return probabilities.map(sequence_probs => {
            // 创建 (概率, 索引) 对
            const prob_index_pairs = sequence_probs.map((prob, idx) => ({ prob, idx }));

            // 按概率降序排序
            prob_index_pairs.sort((a, b) => b.prob - a.prob);

            // 只保留前k个
            const top_k = prob_index_pairs.slice(0, k);

            // 重新归一化
            const sum_top_k = top_k.reduce((sum, item) => sum + item.prob, 0);

            // 创建新的概率分布
            const new_probs = new Array(sequence_probs.length).fill(0);
            top_k.forEach(item => {
                new_probs[item.idx] = item.prob / sum_top_k;
            });

            return new_probs;
        });
    }

    // 核采样 (Nucleus Sampling) - 动态选择概率质量
    nucleusSampling(probabilities, p = 0.9) {
        return probabilities.map(sequence_probs => {
            // 创建 (概率, 索引) 对并排序
            const prob_index_pairs = sequence_probs.map((prob, idx) => ({ prob, idx }));
            prob_index_pairs.sort((a, b) => b.prob - a.prob);

            // 累积概率直到达到p
            let cumulative_prob = 0;
            const nucleus = [];

            for (const item of prob_index_pairs) {
                cumulative_prob += item.prob;
                nucleus.push(item);
                if (cumulative_prob >= p) break;
            }

            // 重新归一化
            const sum_nucleus = nucleus.reduce((sum, item) => sum + item.prob, 0);

            // 创建新的概率分布
            const new_probs = new Array(sequence_probs.length).fill(0);
            nucleus.forEach(item => {
                new_probs[item.idx] = item.prob / sum_nucleus;
            });

            return new_probs;
        });
    }

    // 前向传播
    forward(hidden_states, temperature = 1.0, sampling_method = 'greedy', k = 50, p = 0.9) {
        // 1. 线性投影: hidden_states -> logits
        let logits = this.matmul(hidden_states, this.output_projection);

        // 2. 添加偏置
        logits = logits.map(sequence_logits =>
            sequence_logits.map((logit, idx) => logit + this.bias[idx])
        );

        // 3. 温度缩放
        logits = this.temperatureScale(logits, temperature);

        // 4. Softmax得到概率分布
        let probabilities = this.softmax(logits);

        // 5. 应用采样策略
        switch (sampling_method) {
            case 'top_k':
                probabilities = this.topKSampling(probabilities, k);
                break;
            case 'nucleus':
                probabilities = this.nucleusSampling(probabilities, p);
                break;
            case 'greedy':
            default:
                // 贪婪解码：直接使用原始概率分布
                break;
        }

        return {
            logits: logits,
            probabilities: probabilities
        };
    }

    // 预测下一个词
    predictNextToken(hidden_state, vocab, temperature = 1.0, method = 'greedy') {
        const result = this.forward([hidden_state], temperature, method);
        const probs = result.probabilities[0];

        // 找到概率最高的词
        let max_prob = -1;
        let predicted_token_id = 0;

        probs.forEach((prob, idx) => {
            if (prob > max_prob) {
                max_prob = prob;
                predicted_token_id = idx;
            }
        });

        return {
            token_id: predicted_token_id,
            token: vocab[predicted_token_id] || '<UNK>',
            probability: max_prob,
            all_probabilities: probs
        };
    }
}

// 使用示例
const embed_dim = 512;
const vocab_size = 50000;  // 假设词汇表大小
const vocab = ['我', '喜', '欢', '学', '习', 'AI', '技', '术', '<UNK>', '<PAD>'];  // 简化词汇表

const output_layer = new OutputPredictionLayer(embed_dim, vocab.length);

// 模拟Transformer最后一层的隐藏状态
const final_hidden_states = [
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1),
    new Array(512).fill(0).map(() => Math.random() * 0.1)
];

// 不同的预测方法
console.log("=== 贪婪解码 ===");
const greedy_result = output_layer.forward(final_hidden_states, 1.0, 'greedy');
console.log("输出形状:", \`\${greedy_result.probabilities.length} × \${greedy_result.probabilities[0].length}\`);

console.log("\\n=== Top-K采样 ===");
const topk_result = output_layer.forward(final_hidden_states, 1.0, 'top_k', 5);

console.log("\\n=== 核采样 ===");
const nucleus_result = output_layer.forward(final_hidden_states, 1.0, 'nucleus', 50, 0.9);

// 预测下一个词
const next_token = output_layer.predictNextToken(final_hidden_states[0], vocab);
console.log("\\n=== 下一词预测 ===");
console.log("预测词汇:", next_token.token);
console.log("预测概率:", next_token.probability.toFixed(4));`
    }
};

// 导出供其他文件使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TransformerCoreCode;
}

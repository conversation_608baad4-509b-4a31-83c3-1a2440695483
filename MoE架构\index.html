<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek MoE架构交互演示</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🧠 DeepSeek MoE架构交互演示</h1>
            <p>体验混合专家模型如何智能选择最适合的专家来处理不同类型的任务</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🎯 输入您的问题，看看MoE如何工作</h3>
            <div class="input-section">
                <input type="text" class="input-box" id="userInput" placeholder="例如：写一首诗、解决数学问题、翻译文本、编写代码..."
                    value="帮我写一首关于春天的诗">
                <button class="process-btn" id="processBtn" onclick="processMoE()">
                    🚀 开始处理
                </button>
            </div>
            <div class="button-group">
                <button onclick="loadRandomExample()" class="secondary-btn">
                    🎲 随机示例
                </button>
                <button onclick="showMoEInfo()" class="info-btn">
                    ℹ️ 什么是MoE？
                </button>
            </div>
        </div>

        <!-- MoE架构可视化 -->
        <div class="moe-architecture">
            <div class="architecture-header">
                <h2 class="architecture-title">DeepSeek MoE 处理流程</h2>
                <!-- <p class="architecture-subtitle">观察AI如何智能选择专家处理您的问题</p> -->
            </div>

            <!-- 输入层 -->
            <div class="input-layer" id="inputLayer">
                📝 输入文本
            </div>

            <!-- 路由器 -->
            <div class="router" id="router">
                🎯<br>智能路由器
            </div>

            <!-- 专家模块 -->
            <div class="experts-container">
                <div class="expert" id="expert1" data-type="creative">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🎨</div>
                    <div class="expert-name">创意写作专家</div>
                </div>

                <div class="expert" id="expert2" data-type="math">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🔢</div>
                    <div class="expert-name">数学计算专家</div>
                </div>

                <div class="expert" id="expert3" data-type="code">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">💻</div>
                    <div class="expert-name">编程代码专家</div>
                </div>

                <div class="expert" id="expert4" data-type="language">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">🌐</div>
                    <div class="expert-name">语言翻译专家</div>
                </div>

                <div class="expert" id="expert5" data-type="analysis">
                    <div class="expert-score">0</div>
                    <div class="expert-icon">📊</div>
                    <div class="expert-name">数据分析专家</div>
                </div>
            </div>

            <!-- 输出层 -->
            <div class="output-layer" id="outputLayer">
                ✨ 处理结果
            </div>

            <!-- 连接线 -->
            <div class="connection-line" id="line1"
                style="top: 180px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
            <div class="connection-line" id="line2"
                style="top: 330px; left: 30%; width: 100px; transform: rotate(-30deg);"></div>
            <div class="connection-line" id="line3"
                style="top: 330px; left: 45%; width: 80px; transform: rotate(-15deg);"></div>
            <div class="connection-line" id="line4"
                style="top: 330px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
            <div class="connection-line" id="line5"
                style="top: 330px; right: 45%; width: 80px; transform: rotate(15deg);"></div>
            <div class="connection-line" id="line6"
                style="top: 330px; right: 30%; width: 100px; transform: rotate(30deg);"></div>
            <div class="connection-line" id="line7"
                style="bottom: 120px; left: 50%; width: 80px; transform: translateX(-50%);"></div>
        </div>

        <!-- 实时解释面板 -->
        <div class="live-explanation">
            <h3>🔍 实时处理解释</h3>
            <div class="explanation-content" id="liveExplanation">
                <div class="explanation-item active" id="explanation0">
                    <div class="explanation-icon">🎯</div>
                    <div class="explanation-text">
                        <strong>准备阶段</strong><br>
                        输入您的问题，MoE系统将开始智能分析。系统会识别问题类型，然后决定激活哪些专家来处理。
                    </div>
                </div>

                <div class="explanation-item" id="explanation1">
                    <div class="explanation-icon">📝</div>
                    <div class="explanation-text">
                        <strong>输入分析中...</strong><br>
                        系统正在分析您的输入文本，提取关键特征和语义信息，为后续的专家选择做准备。
                    </div>
                </div>

                <div class="explanation-item" id="explanation2">
                    <div class="explanation-icon">🎯</div>
                    <div class="explanation-text">
                        <strong>智能路由计算</strong><br>
                        路由器正在计算每个专家的匹配度分数。分数越高，说明该专家越适合处理当前任务。
                    </div>
                </div>

                <div class="explanation-item" id="explanation3">
                    <div class="explanation-icon">🧠</div>
                    <div class="explanation-text">
                        <strong>专家协作处理</strong><br>
                        被选中的专家开始并行工作。每个专家都有自己的专业领域，能够提供高质量的处理结果。
                    </div>
                </div>

                <div class="explanation-item" id="explanation4">
                    <div class="explanation-icon">⚡</div>
                    <div class="explanation-text">
                        <strong>结果融合输出</strong><br>
                        系统将各个专家的输出进行智能融合，生成最终的高质量结果。这就是MoE的强大之处！
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细说明面板 -->
        <div class="explanation-panel">
            <h3>📚 MoE架构核心优势</h3>
            <div class="advantages-grid">
                <div class="advantage-item">
                    <div class="advantage-icon">🎯</div>
                    <h4>专业化分工</h4>
                    <p>每个专家专注于特定领域，如创意写作、数学计算等，确保在各自领域达到最佳性能。</p>
                </div>

                <div class="advantage-item">
                    <div class="advantage-icon">⚡</div>
                    <h4>高效计算</h4>
                    <p>只激活需要的专家，避免浪费计算资源，实现高效的稀疏计算模式。</p>
                </div>

                <div class="advantage-item">
                    <div class="advantage-icon">🧠</div>
                    <h4>智能选择</h4>
                    <p>路由器能够智能分析任务特征，自动选择最适合的专家组合来处理问题。</p>
                </div>

                <div class="advantage-item">
                    <div class="advantage-icon">🚀</div>
                    <h4>扩展性强</h4>
                    <p>可以轻松添加新的专家模块，不断扩展系统的能力边界。</p>
                </div>
            </div>
        </div>

        <!-- 静态图示部分 -->
        <div class="moe-diagram">
            <h3>🎨 MoE架构核心原理图示</h3>

            <!-- 处理流程图 -->
            <div class="diagram-flow">
                <div class="flow-step">
                    <div class="flow-icon">📝</div>
                    <div class="flow-label">输入任务</div>
                </div>

                <div class="diagram-arrow">→</div>

                <div class="flow-step">
                    <div class="flow-icon">🎯</div>
                    <div class="flow-label">智能路由</div>
                </div>

                <div class="diagram-arrow">→</div>

                <div class="flow-step">
                    <div class="flow-icon">🧠</div>
                    <div class="flow-label">专家选择</div>
                </div>

                <div class="diagram-arrow">→</div>

                <div class="flow-step">
                    <div class="flow-icon">⚡</div>
                    <div class="flow-label">结果融合</div>
                </div>
            </div>

            <!-- 架构示意图 -->
            <div class="diagram-container">
                <div class="diagram-section">
                    <div class="diagram-input">任务输入</div>
                </div>

                <div class="diagram-section">
                    <div class="diagram-router">路由器</div>
                </div>

                <div class="diagram-section">
                    <div class="diagram-experts">
                        <div class="diagram-expert active">🎨 创意专家</div>
                        <div class="diagram-expert">🔢 数学专家</div>
                        <div class="diagram-expert active">💻 编程专家</div>
                        <div class="diagram-expert">🌐 翻译专家</div>
                        <div class="diagram-expert">📊 分析专家</div>
                    </div>
                </div>

                <div class="diagram-section">
                    <div class="diagram-output">最终输出</div>
                </div>
            </div>

            <!-- 核心概念说明 -->
            <div class="concept-grid">
                <div class="concept-item">
                    <div class="concept-title">🎯 稀疏激活</div>
                    <div class="concept-desc">
                        只激活处理当前任务最相关的专家，而不是所有专家同时工作，大大提高了计算效率。
                    </div>
                </div>

                <div class="concept-item">
                    <div class="concept-title">🧠 专家分工</div>
                    <div class="concept-desc">
                        每个专家专注于特定领域，通过专业化分工实现更高质量的任务处理。
                    </div>
                </div>

                <div class="concept-item">
                    <div class="concept-title">⚖️ 负载均衡</div>
                    <div class="concept-desc">
                        智能路由器确保任务在专家之间合理分配，避免某些专家过载而其他专家闲置。
                    </div>
                </div>

                <div class="concept-item">
                    <div class="concept-title">🔄 动态选择</div>
                    <div class="concept-desc">
                        根据输入内容的特征动态选择最适合的专家组合，实现灵活高效的处理。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>
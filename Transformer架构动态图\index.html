<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transformer架构动态图</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <header>
            <h1>🤖 Transformer架构动态演示</h1>
            <p class="subtitle">深入理解编码器-解码器架构的工作原理</p>
        </header>

        <!-- 架构说明 -->
        <div class="architecture-info">
            <h2>📋 Transformer架构图</h2>
            <p>下图展示了Transformer的完整架构，包括编码器-解码器结构、注意力机制、前馈网络等核心组件</p>
        </div>

        <!-- 主要架构图 -->
        <div class="architecture-container">
            <div class="transformer-architecture">
                <!-- 整体架构图 -->
                <div class="architecture-layout">

                    <!-- 输入部分 -->
                    <div class="input-section">
                        <div class="input-text" id="inputs">
                            <div class="input-label">输入序列</div>
                            <div class="input-tokens">
                                <span class="token">我</span>
                                <span class="token">爱</span>
                                <span class="token">学习</span>
                                <span class="token">AI</span>
                            </div>
                        </div>

                        <div class="arrow-down"></div>

                        <div class="embedding-layer" id="input-embedding">
                            <div class="layer-title">输入嵌入层</div>
                            <div class="layer-subtitle">Input Embedding</div>
                            <div class="knowledge-tooltip">
                                💡 将词汇转换为高维向量表示，每个词对应一个固定维度的向量
                            </div>
                        </div>
                    </div>

                    <!-- 主体架构 -->
                    <div class="main-architecture">

                        <!-- 编码器部分 -->
                        <div class="encoder-section">
                            <div class="section-label encoder-label">编码器 (Encoder)</div>
                            <div class="section-note">N× (通常6层)</div>

                            <!-- 位置编码 -->
                            <div class="positional-encoding" id="encoder-pos">
                                <div class="pos-circle">⊕</div>
                                <div class="pos-label">位置编码<br>Positional Encoding</div>
                                <div class="knowledge-tooltip">
                                    💡 为每个位置添加位置信息，让模型理解词语的顺序关系
                                </div>
                            </div>

                            <div class="arrow-down"></div>

                            <!-- 编码器层 -->
                            <div class="encoder-layer" id="encoder-block">

                                <!-- 多头自注意力 -->
                                <div class="attention-block" id="encoder-attention">
                                    <div class="block-title">多头自注意力</div>
                                    <div class="block-subtitle">Multi-Head Attention</div>
                                    <div class="attention-heads">
                                        <div class="head">H1</div>
                                        <div class="head">H2</div>
                                        <div class="head">H3</div>
                                        <div class="head">H4</div>
                                    </div>
                                    <div class="knowledge-tooltip">
                                        💡 让序列中每个词都能关注到其他所有词，捕获全局依赖关系
                                    </div>
                                </div>

                                <!-- 残差连接和归一化 -->
                                <div class="add-norm-block" id="encoder-norm1">
                                    <div class="residual-arrow">↗</div>
                                    <div class="norm-box">
                                        <div class="norm-title">Add & Norm</div>
                                        <div class="norm-subtitle">残差连接 + 层归一化</div>
                                    </div>
                                    <div class="residual-arrow">↘</div>
                                    <div class="knowledge-tooltip">
                                        💡 残差连接保留原始信息，层归一化稳定训练过程
                                    </div>
                                </div>

                                <!-- 前馈网络 -->
                                <div class="ffn-block" id="encoder-ffn">
                                    <div class="block-title">前馈网络</div>
                                    <div class="block-subtitle">Feed Forward</div>
                                    <div class="ffn-structure">
                                        <div class="ffn-layer">Linear</div>
                                        <div class="ffn-activation">ReLU</div>
                                        <div class="ffn-layer">Linear</div>
                                    </div>
                                    <div class="knowledge-tooltip">
                                        💡 两层全连接网络，对每个位置独立进行非线性变换
                                    </div>
                                </div>

                                <!-- 第二个残差连接和归一化 -->
                                <div class="add-norm-block" id="encoder-norm2">
                                    <div class="residual-arrow">↗</div>
                                    <div class="norm-box">
                                        <div class="norm-title">Add & Norm</div>
                                        <div class="norm-subtitle">残差连接 + 层归一化</div>
                                    </div>
                                    <div class="residual-arrow">↘</div>
                                </div>
                            </div>

                            <!-- 编码器到解码器的连接箭头 -->
                            <div class="connection-arrow">→</div>
                        </div>

                        <!-- 解码器部分 -->
                        <div class="decoder-section">
                            <div class="section-label decoder-label">解码器 (Decoder)</div>
                            <div class="section-note">N× (通常6层)</div>

                            <!-- 位置编码 -->
                            <div class="positional-encoding" id="decoder-pos">
                                <div class="pos-circle">⊕</div>
                                <div class="pos-label">位置编码<br>Positional Encoding</div>
                                <div class="knowledge-tooltip">
                                    💡 为目标序列添加位置信息
                                </div>
                            </div>

                            <!-- 解码器层 -->
                            <div class="decoder-layer" id="decoder-block">

                                <!-- 掩码多头自注意力 -->
                                <div class="attention-block masked" id="decoder-masked-attention">
                                    <div class="block-title">掩码多头自注意力</div>
                                    <div class="block-subtitle">Masked Multi-Head Attention</div>
                                    <div class="mask-indicator">🔒 掩码</div>
                                    <div class="knowledge-tooltip">
                                        💡 防止解码器看到未来的词，保持自回归生成特性
                                    </div>
                                </div>

                                <!-- 第一个残差连接和归一化 -->
                                <div class="add-norm-block" id="decoder-norm1">
                                    <div class="residual-arrow">↗</div>
                                    <div class="norm-box">
                                        <div class="norm-title">Add & Norm</div>
                                        <div class="norm-subtitle">残差连接 + 层归一化</div>
                                    </div>
                                    <div class="residual-arrow">↘</div>
                                </div>

                                <!-- 编码器-解码器注意力 -->
                                <div class="attention-block cross" id="decoder-cross-attention">
                                    <div class="block-title">多头注意力</div>
                                    <div class="block-subtitle">Multi-Head Attention</div>
                                    <div class="cross-connection">
                                        <div class="query-line">Q (来自解码器)</div>
                                        <div class="kv-line">K,V (来自编码器)</div>
                                    </div>
                                    <div class="knowledge-tooltip">
                                        💡 解码器通过注意力机制关注编码器的输出，建立源序列和目标序列的联系
                                    </div>
                                </div>

                                <!-- 第二个残差连接和归一化 -->
                                <div class="add-norm-block" id="decoder-norm2">
                                    <div class="residual-arrow">↗</div>
                                    <div class="norm-box">
                                        <div class="norm-title">Add & Norm</div>
                                        <div class="norm-subtitle">残差连接 + 层归一化</div>
                                    </div>
                                    <div class="residual-arrow">↘</div>
                                </div>

                                <!-- 前馈网络 -->
                                <div class="ffn-block" id="decoder-ffn">
                                    <div class="block-title">前馈网络</div>
                                    <div class="block-subtitle">Feed Forward</div>
                                    <div class="ffn-structure">
                                        <div class="ffn-layer">Linear</div>
                                        <div class="ffn-activation">ReLU</div>
                                        <div class="ffn-layer">Linear</div>
                                    </div>
                                    <div class="knowledge-tooltip">
                                        💡 与编码器相同的前馈网络结构
                                    </div>
                                </div>

                                <!-- 第三个残差连接和归一化 -->
                                <div class="add-norm-block" id="decoder-norm3">
                                    <div class="residual-arrow">↗</div>
                                    <div class="norm-box">
                                        <div class="norm-title">Add & Norm</div>
                                        <div class="norm-subtitle">残差连接 + 层归一化</div>
                                    </div>
                                    <div class="residual-arrow">↘</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 线性层部分 - 右上角 -->
                    <div class="linear-section">
                        <div class="linear-layer" id="linear-layer">
                            <div class="layer-title">线性层</div>
                            <div class="layer-subtitle">Linear</div>
                            <div class="knowledge-tooltip">
                                💡 将解码器输出映射到词汇表大小的向量
                            </div>
                        </div>
                    </div>

                    <!-- 输出部分 - 右下角 -->
                    <div class="output-section">
                        <div class="softmax-layer" id="softmax-layer">
                            <div class="layer-title">Softmax</div>
                            <div class="layer-subtitle">概率分布</div>
                            <div class="knowledge-tooltip">
                                💡 将线性层输出转换为词汇表上的概率分布
                            </div>
                        </div>

                        <div class="output-text" id="outputs">
                            <div class="output-label">输出概率</div>
                            <div class="output-probs">
                                <div class="prob-item">I: 0.85</div>
                                <div class="prob-item">love: 0.92</div>
                                <div class="prob-item">learning: 0.78</div>
                                <div class="prob-item">AI: 0.95</div>
                            </div>
                        </div>
                    </div>

                    <!-- 目标输入 -->
                    <div class="target-input-section">
                        <div class="target-text" id="target-inputs">
                            <div class="input-label">目标序列输入<br>(右移)</div>
                            <div class="input-tokens">
                                <span class="token">&lt;start&gt;</span>
                                <span class="token">I</span>
                                <span class="token">love</span>
                                <span class="token">learning</span>
                            </div>
                        </div>

                        <div class="embedding-layer" id="target-embedding">
                            <div class="layer-title">输出嵌入层</div>
                            <div class="layer-subtitle">Output Embedding</div>
                            <div class="knowledge-tooltip">
                                💡 将目标序列转换为向量表示（训练时）或已生成的部分（推理时）
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据流动指示器 -->
        <div class="flow-indicators" id="flow-indicators">
            <!-- 动态生成的流动指示器 -->
        </div>

        <!-- 知识点总结 -->
        <div class="knowledge-summary">
            <h2>📚 核心知识点总结</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>🔄 编码器-解码器架构</h3>
                    <p>编码器将输入序列转化为上下文表示，解码器根据这些表示生成目标序列</p>
                </div>
                <div class="summary-card">
                    <h3>🎯 注意力机制</h3>
                    <p>自注意力让模型关注序列内部关系，交叉注意力连接源序列和目标序列</p>
                </div>
                <div class="summary-card">
                    <h3>🔗 残差连接</h3>
                    <p>避免深层网络的梯度消失问题，保留原始信息</p>
                </div>
                <div class="summary-card">
                    <h3>📏 层归一化</h3>
                    <p>加快训练速度，提高模型稳定性</p>
                </div>
            </div>
        </div>
    </div>

    <script src="script_new.js"></script>
</body>

</html>
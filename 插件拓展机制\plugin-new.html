<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件拓展机制高端架构图示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
        }

        .title {
            text-align: center;
            margin-bottom: 50px;
        }

        .title h1 {
            color: #2c3e50;
            font-size: 3em;
            margin-bottom: 15px;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title p {
            color: #7f8c8d;
            font-size: 1.4em;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            font-weight: 500;
        }

        /* 主要对比区域 */
        .main-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            margin: 60px 0;
            position: relative;
        }

        /* 分隔线 */
        .divider {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, transparent, #e9ecef 20%, #667eea 50%, #e9ecef 80%, transparent);
            transform: translateX(-50%);
            z-index: 5;
        }

        .divider::before {
            content: "VS";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            color: #667eea;
            font-weight: 800;
            font-size: 1.5em;
            padding: 15px 20px;
            border-radius: 50%;
            border: 4px solid #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            z-index: 10;
        }

        /* 系统架构面板 */
        .system-architecture {
            position: relative;
            background: radial-gradient(circle at center, #f8f9fa, #e9ecef);
            border-radius: 30px;
            padding: 40px;
            min-height: 800px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 2px solid #dee2e6;
            overflow: hidden;
        }

        .system-architecture.basic {
            background: radial-gradient(circle at center, #f8f9fa, #e9ecef);
            border-color: #6c757d;
        }

        .system-architecture.extended {
            background: radial-gradient(circle at center, #f0fff4, #e8f5e8);
            border-color: #28a745;
        }

        .system-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .system-header h2 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 800;
        }

        .system-architecture.basic .system-header h2 {
            color: #6c757d;
        }

        .system-architecture.extended .system-header h2 {
            color: #28a745;
        }

        .system-header p {
            color: #7f8c8d;
            font-size: 1.2em;
            font-weight: 600;
        }

        /* AI核心系统 */
        .ai-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 800;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.4);
            z-index: 10;
            border: 5px solid white;
        }

        .ai-core .icon {
            font-size: 4em;
            margin-bottom: 8px;
            filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
        }

        .ai-core .label {
            font-size: 1.1em;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* 连接线 */
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, #667eea, transparent);
            height: 3px;
            transform-origin: left center;
            z-index: 5;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
        }

        .connection-line.extended {
            background: linear-gradient(90deg, #28a745, transparent);
            box-shadow: 0 1px 3px rgba(40, 167, 69, 0.3);
        }

        /* 功能节点 */
        .function-node {
            position: absolute;
            width: 120px;
            height: 80px;
            background: white;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
            border: 3px solid #667eea;
            z-index: 8;
            transition: all 0.3s ease;
        }

        .function-node:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.3);
        }

        .function-node .node-icon {
            font-size: 2.2em;
            margin-bottom: 8px;
            color: #667eea;
        }

        .function-node .node-text {
            font-size: 0.9em;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            line-height: 1.2;
        }

        /* 插件节点 */
        .plugin-node {
            position: absolute;
            width: 110px;
            height: 110px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 15px 45px rgba(40, 167, 69, 0.25);
            border: 4px solid #28a745;
            z-index: 8;
            transition: all 0.3s ease;
        }

        .plugin-node:hover {
            transform: translateY(-8px) scale(1.08);
            box-shadow: 0 25px 60px rgba(40, 167, 69, 0.4);
        }

        .plugin-node .node-icon {
            font-size: 2.5em;
            margin-bottom: 6px;
            color: #28a745;
        }

        .plugin-node .node-text {
            font-size: 0.85em;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            line-height: 1.1;
        }

        .plugin-node .status-dot {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #28a745;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 3px 12px rgba(40, 167, 69, 0.4);
        }

        /* 功能描述面板 */
        .capabilities-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 25px;
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            max-height: 200px;
            overflow-y: auto;
        }

        .capabilities-panel h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.6em;
            font-weight: 800;
            text-align: center;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
        }

        .capability-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 18px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .capability-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .system-architecture.extended .capability-item.plugin-capability {
            border-left-color: #28a745;
            background: rgba(240, 255, 244, 0.9);
        }

        .capability-item .capability-icon {
            font-size: 1.5em;
            margin-right: 10px;
            vertical-align: middle;
        }

        .capability-item .capability-text {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1em;
        }

        .capability-item.plugin-capability .capability-text {
            color: #28a745;
        }

        /* 解耦概念说明 */
        .decoupling-explanation {
            background: linear-gradient(135deg, #f0f4ff, #e6f3ff);
            border-radius: 30px;
            padding: 50px;
            margin-top: 60px;
            border: 3px solid #667eea;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.15);
        }

        .decoupling-explanation h3 {
            color: #667eea;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-align: center;
            font-weight: 800;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .decoupling-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .decoupling-item {
            background: white;
            padding: 30px;
            border-radius: 20px;
            border-left: 6px solid #667eea;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
            transition: all 0.3s ease;
        }

        .decoupling-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(102, 126, 234, 0.25);
        }

        .decoupling-item h4 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 800;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .decoupling-item p {
            color: #495057;
            line-height: 1.8;
            margin-bottom: 20px;
            font-size: 1.1em;
        }

        .decoupling-item ul {
            color: #495057;
            line-height: 1.7;
            padding-left: 25px;
        }

        .decoupling-item li {
            margin-bottom: 12px;
            font-size: 1.05em;
        }

        .highlight-text {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #f39c12;
            margin-top: 30px;
            font-weight: 700;
            color: #856404;
            font-size: 1.2em;
            text-align: center;
            box-shadow: 0 5px 20px rgba(243, 156, 18, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .main-comparison {
                gap: 60px;
            }

            .system-architecture {
                min-height: 700px;
                padding: 35px;
            }

            .ai-core {
                width: 130px;
                height: 130px;
            }

            .function-node {
                width: 100px;
                height: 70px;
            }

            .plugin-node {
                width: 90px;
                height: 90px;
            }
        }

        @media (max-width: 1200px) {
            .main-comparison {
                gap: 40px;
            }

            .system-architecture {
                min-height: 650px;
                padding: 30px;
            }

            .ai-core {
                width: 120px;
                height: 120px;
            }

            .function-node {
                width: 90px;
                height: 60px;
                font-size: 0.85em;
            }

            .plugin-node {
                width: 80px;
                height: 80px;
                font-size: 0.8em;
            }

            .decoupling-content {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 25px;
            }

            .title h1 {
                font-size: 2.2em;
            }

            .main-comparison {
                grid-template-columns: 1fr;
                gap: 50px;
            }

            .divider {
                display: none;
            }

            .system-architecture {
                min-height: 550px;
                padding: 25px;
            }

            .ai-core {
                width: 100px;
                height: 100px;
            }

            .ai-core .icon {
                font-size: 2.8em;
            }

            .function-node {
                width: 80px;
                height: 50px;
                font-size: 0.75em;
            }

            .plugin-node {
                width: 70px;
                height: 70px;
                font-size: 0.7em;
            }

            .capabilities-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .decoupling-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .decoupling-explanation {
                padding: 30px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }

            .title h1 {
                font-size: 1.8em;
            }

            .system-architecture {
                padding: 20px;
                min-height: 500px;
            }

            .ai-core {
                width: 80px;
                height: 80px;
            }

            .ai-core .icon {
                font-size: 2.2em;
            }

            .function-node {
                width: 70px;
                height: 45px;
                font-size: 0.65em;
            }

            .plugin-node {
                width: 60px;
                height: 60px;
                font-size: 0.6em;
            }

            .capabilities-panel {
                padding: 20px;
            }

            .decoupling-explanation {
                padding: 25px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="title">
            <h1>🔌 插件拓展机制高端架构</h1>
            <p>展示AI系统如何在不修改核心代码的情况下，通过外部插件扩展功能。左侧为基础系统，右侧为扩展后的系统，核心代码保持完全不变。</p>
        </div>

        <div class="main-comparison">
            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 基础AI系统架构 -->
            <div class="system-architecture basic">
                <div class="system-header">
                    <h2>🔲 基础AI系统</h2>
                    <p>仅包含核心功能模块</p>
                </div>

                <!-- AI核心 -->
                <div class="ai-core">
                    <div class="icon">🤖</div>
                    <div class="label">AI核心</div>
                </div>

                <!-- 基础功能节点 -->
                <div class="function-node" style="top: 8%; left: 8%;">
                    <div class="node-icon">💬</div>
                    <div class="node-text">自然语言<br>处理</div>
                </div>

                <div class="function-node" style="top: 8%; right: 8%;">
                    <div class="node-icon">🧠</div>
                    <div class="node-text">机器学习<br>推理</div>
                </div>

                <div class="function-node" style="bottom: 8%; left: 8%;">
                    <div class="node-icon">📝</div>
                    <div class="node-text">文本<br>生成</div>
                </div>

                <div class="function-node" style="bottom: 8%; right: 8%;">
                    <div class="node-icon">🔍</div>
                    <div class="node-text">信息<br>检索</div>
                </div>

                <!-- 连接线 - 从中心到四个角落 -->
                <div class="connection-line"
                    style="top: 50%; left: 50%; width: 35%; transform: translate(-50%, -50%) rotate(-135deg) translateX(-50%);">
                </div>
                <div class="connection-line"
                    style="top: 50%; left: 50%; width: 35%; transform: translate(-50%, -50%) rotate(-45deg) translateX(-50%);">
                </div>
                <div class="connection-line"
                    style="top: 50%; left: 50%; width: 35%; transform: translate(-50%, -50%) rotate(45deg) translateX(-50%);">
                </div>
                <div class="connection-line"
                    style="top: 50%; left: 50%; width: 35%; transform: translate(-50%, -50%) rotate(135deg) translateX(-50%);">
                </div>

                <!-- 功能描述 -->
                <div class="capabilities-panel">
                    <h3>🔧 系统能力</h3>
                    <div class="capabilities-grid">
                        <div class="capability-item">
                            <span class="capability-icon">💬</span>
                            <span class="capability-text">理解和处理自然语言</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🧠</span>
                            <span class="capability-text">执行机器学习推理</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">📝</span>
                            <span class="capability-text">生成文本内容</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🔍</span>
                            <span class="capability-text">检索相关信息</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 扩展AI系统架构 -->
            <div class="system-architecture extended">
                <div class="system-header">
                    <h2>🌟 扩展AI系统</h2>
                    <p>通过插件机制扩展功能</p>
                </div>

                <!-- AI核心（相同） -->
                <div class="ai-core">
                    <div class="icon">🤖</div>
                    <div class="label">AI核心</div>
                </div>

                <!-- 基础功能节点（内层） -->
                <div class="function-node" style="top: 15%; left: 15%;">
                    <div class="node-icon">💬</div>
                    <div class="node-text">自然语言<br>处理</div>
                </div>

                <div class="function-node" style="top: 15%; right: 15%;">
                    <div class="node-icon">🧠</div>
                    <div class="node-text">机器学习<br>推理</div>
                </div>

                <div class="function-node" style="bottom: 15%; left: 15%;">
                    <div class="node-icon">📝</div>
                    <div class="node-text">文本<br>生成</div>
                </div>

                <div class="function-node" style="bottom: 15%; right: 15%;">
                    <div class="node-icon">🔍</div>
                    <div class="node-text">信息<br>检索</div>
                </div>

                <!-- 插件节点（外层） -->
                <div class="plugin-node" style="top: 2%; left: 50%; transform: translateX(-50%);">
                    <div class="node-icon">🌤️</div>
                    <div class="node-text">天气<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <div class="plugin-node" style="top: 25%; left: 2%;">
                    <div class="node-icon">🧮</div>
                    <div class="node-text">计算<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <div class="plugin-node" style="top: 25%; right: 2%;">
                    <div class="node-icon">🌐</div>
                    <div class="node-text">翻译<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <div class="plugin-node" style="bottom: 25%; left: 2%;">
                    <div class="node-icon">🗄️</div>
                    <div class="node-text">数据库<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <div class="plugin-node" style="bottom: 25%; right: 2%;">
                    <div class="node-icon">🖼️</div>
                    <div class="node-text">图像<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <div class="plugin-node" style="bottom: 2%; left: 50%; transform: translateX(-50%);">
                    <div class="node-icon">🎤</div>
                    <div class="node-text">语音<br>插件</div>
                    <div class="status-dot"></div>
                </div>

                <!-- 连接线 - 太阳发散效果，从中心到所有节点 -->
                <!-- 到基础功能节点 -->
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 30%; transform: translate(-50%, -50%) rotate(-135deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 30%; transform: translate(-50%, -50%) rotate(-45deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 30%; transform: translate(-50%, -50%) rotate(45deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 30%; transform: translate(-50%, -50%) rotate(135deg) translateX(-50%);">
                </div>

                <!-- 到插件节点 -->
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(-90deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(-150deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(-30deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(30deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(150deg) translateX(-50%);">
                </div>
                <div class="connection-line extended"
                    style="top: 50%; left: 50%; width: 45%; transform: translate(-50%, -50%) rotate(90deg) translateX(-50%);">
                </div>

                <!-- 功能描述 -->
                <div class="capabilities-panel">
                    <h3>🚀 扩展能力</h3>
                    <div class="capabilities-grid">
                        <div class="capability-item">
                            <span class="capability-icon">💬</span>
                            <span class="capability-text">理解和处理自然语言</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🧠</span>
                            <span class="capability-text">执行机器学习推理</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">📝</span>
                            <span class="capability-text">生成文本内容</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🔍</span>
                            <span class="capability-text">检索相关信息</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🌤️</span>
                            <span class="capability-text">实时天气查询</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🧮</span>
                            <span class="capability-text">复杂数学计算</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🌐</span>
                            <span class="capability-text">多语言翻译</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🗄️</span>
                            <span class="capability-text">数据库操作</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🖼️</span>
                            <span class="capability-text">图像处理分析</span>
                        </div>
                        <div class="capability-item plugin-capability">
                            <span class="capability-icon">🎤</span>
                            <span class="capability-text">语音识别转换</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 解耦概念深度解析 -->
        <div class="decoupling-explanation">
            <h3>🔗 解耦机制深度解析</h3>
            <div class="decoupling-content">
                <div class="decoupling-item">
                    <h4>🎯 什么是解耦？</h4>
                    <p>解耦是指将系统中相互依赖的组件分离，使它们能够独立开发、测试、部署和维护。在插件架构中，解耦体现为：</p>
                    <ul>
                        <li><strong>功能分离：</strong>核心功能与扩展功能完全分开</li>
                        <li><strong>接口隔离：</strong>通过标准化接口进行通信</li>
                        <li><strong>依赖倒置：</strong>核心系统不依赖具体插件实现</li>
                        <li><strong>独立演进：</strong>各组件可独立更新升级</li>
                    </ul>
                </div>

                <div class="decoupling-item">
                    <h4>🔌 接口标准化</h4>
                    <p>标准化接口是解耦的关键，它定义了核心系统与插件之间的通信协议：</p>
                    <ul>
                        <li><strong>统一规范：</strong>所有插件遵循相同的接口定义</li>
                        <li><strong>数据格式：</strong>标准化的输入输出数据结构</li>
                        <li><strong>生命周期：</strong>插件的加载、初始化、执行、卸载流程</li>
                        <li><strong>错误处理：</strong>统一的异常处理和错误报告机制</li>
                    </ul>
                </div>

                <div class="decoupling-item">
                    <h4>🚀 动态扩展优势</h4>
                    <p>解耦架构支持系统的动态扩展，带来显著优势：</p>
                    <ul>
                        <li><strong>零停机扩展：</strong>无需重启系统即可添加新功能</li>
                        <li><strong>按需加载：</strong>根据实际需求动态加载相应插件</li>
                        <li><strong>资源优化：</strong>只加载必要的功能模块，节省资源</li>
                        <li><strong>快速迭代：</strong>新功能可独立开发和部署</li>
                    </ul>
                </div>

                <div class="decoupling-item">
                    <h4>⚙️ 实际应用价值</h4>
                    <p>解耦的插件机制在实际应用中具有重要价值：</p>
                    <ul>
                        <li><strong>降低复杂度：</strong>将复杂系统分解为简单模块</li>
                        <li><strong>提高可维护性：</strong>问题定位和修复更加容易</li>
                        <li><strong>支持团队协作：</strong>不同团队可并行开发不同插件</li>
                        <li><strong>增强可测试性：</strong>每个插件可独立测试验证</li>
                    </ul>
                </div>
            </div>

            <div class="highlight-text">
                💡 <strong>核心理念：</strong>通过解耦实现"开放-封闭原则" - 对扩展开放，对修改封闭。核心系统保持稳定，功能通过插件灵活扩展。
            </div>
        </div>

        <script>
            // 简单的交互效果
            document.addEventListener('DOMContentLoaded', function () {
                // 为功能节点添加悬停效果
                const functionNodes = document.querySelectorAll('.function-node');
                functionNodes.forEach(node => {
                    node.addEventListener('mouseenter', function () {
                        this.style.borderColor = '#4CAF50';
                        this.style.boxShadow = '0 20px 50px rgba(76, 175, 80, 0.3)';
                    });

                    node.addEventListener('mouseleave', function () {
                        this.style.borderColor = '#667eea';
                        this.style.boxShadow = '0 12px 35px rgba(0, 0, 0, 0.2)';
                    });
                });

                // 为插件节点添加悬停效果
                const pluginNodes = document.querySelectorAll('.plugin-node');
                pluginNodes.forEach(node => {
                    node.addEventListener('mouseenter', function () {
                        this.style.borderColor = '#FF9800';
                        this.style.boxShadow = '0 25px 60px rgba(255, 152, 0, 0.4)';
                    });

                    node.addEventListener('mouseleave', function () {
                        this.style.borderColor = '#28a745';
                        this.style.boxShadow = '0 15px 45px rgba(40, 167, 69, 0.25)';
                    });
                });

                // 为能力项添加悬停效果
                const capabilityItems = document.querySelectorAll('.capability-item');
                capabilityItems.forEach(item => {
                    item.addEventListener('mouseenter', function () {
                        this.style.borderLeftWidth = '8px';
                    });

                    item.addEventListener('mouseleave', function () {
                        this.style.borderLeftWidth = '5px';
                    });
                });
            });
        </script>
    </div>
</body>

</html>